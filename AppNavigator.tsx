import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator, NativeStackNavigationOptions } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import screens
import SplashScreen from './src/screens/auth/SplashScreen';
import LoginScreen from './src/screens/auth/LoginScreen';
import HomeScreen from './src/screens/main/HomeScreen';
import HelpForm from './src/screens/HelpForm';
import ProgressScreen from './src/screens/main/ProgressScreen';
import MenuScreen from './src/screens/main/MenuScreen';
import ProductDetail from './src/components/ProductDetail';
import ProductListingScreen from './src/screens/main/ProductListingScreen';
import OffersScreen from './src/screens/menu/OffersScreen';
import ApprovalsScreen from './src/screens/menu/ApprovalsScreen';
import OrdersScreen from './src/screens/menu/OrdersScreen';
import SettingsScreen from './src/screens/menu/SettingsScreen';
import RegisterWarrantyScreen from './src/screens/main/RegisterWarrantyScreen';
import WarrantyHistoryScreen from './src/screens/main/WarrantyHistoryScreen';
import WarrantyDetailScreen from './src/screens/main/WarrantyDetailScreen';
import Schemes from './src/components/Schemes';
import AccountScreen from './src/screens/main/AccountScreen';
import CartScreen from './src/screens/cart/CartScreen';
import BuyNowScreen from './src/screens/cart/BuyNowScreen';
import Header from './src/components/Home/Header';
import ThankYouScreen from './src/screens/main/ThankYouScreen';
import AddAddressScreen from './src/screens/cart/AddAddressScreen';
import EditAddressScreen from './src/screens/cart/EditAddressScreen';

// Import context
import { UserProvider, useUser } from './src/context/UserContext';
import { NotificationProvider } from './src/context/NotificationContext';
import { UserRole } from './src/data/mockData';

// Import notification screen
import NotificationScreen from './src/screens/notifications/NotificationScreen';

// Import management screens
import SuperStockistListScreen from './src/screens/management/SuperStockistListScreen';
import SuperStockistDetailScreen from './src/screens/management/SuperStockistDetailScreen';
import DistributorListScreen from './src/screens/management/DistributorListScreen';
import DistributorDetailScreen from './src/screens/management/DistributorDetailScreen';
import RetailerListScreen from './src/screens/management/RetailerListScreen';
import RetailerDetailScreen from './src/screens/management/RetailerDetailScreen';

import LeaderboardScreen from './src/screens/main/LeaderboardScreen';
import UserManagementScreen from './src/screens/management/UserManagementScreen';
import CreateUserScreen from './src/screens/management/CreateUserScreen';
import PricingManagementScreen from './src/screens/management/PricingManagementScreen';
import SchemeManagementScreen from './src/screens/management/SchemeManagementScreen';
import CreateSchemeScreen from './src/screens/management/CreateSchemeScreen';
import OffersManagementScreen from './src/screens/management/OffersManagementScreen';
import CreateEditOfferScreen from './src/screens/management/CreateEditOfferScreen';
import AnnouncementScreen from './src/screens/management/AnnouncementScreen';

// Management navigator
import ManagementNavigator from './src/navigation/ManagementNavigator';
import { navigationRef } from './src/services/api/AuthApiService';
import PricingListScreen from './src/screens/management/PricingListScreen';
import CreatePricingScreen from './src/screens/management/CreatePricingScreen';

// Types
type RootStackParamList = {
  Splash: undefined;
  Login: undefined;
  Register: undefined;
  MainApp: undefined;
  HelpForm: undefined;
  ProductDetail: { product: any };
  ProductListing: { category?: string; categoryId?: number };
  RegisterWarranty: undefined;
  WarrantyHistory: undefined;
  WarrantyDetail: { id: string };
  Schemes: undefined;
  Offers: undefined;
  Approvals: undefined;
  Orders: undefined;
  Settings: undefined;
  Cart: undefined;
  BuyNow: undefined;
  ThankYou: undefined;
  AddAddress: undefined;
  EditAddress: { address: any };
  Notifications: undefined;
  // Management screens
  SuperStockistList: undefined;
  SuperStockistDetail: { id?: string };
  DistributorList: undefined;
  DistributorDetail: { id?: string };
  RetailerList: undefined;
  RetailerDetail: { id?: string };
  DiscountSchemeList: undefined;
  DiscountSchemeDetail: { id?: string };
  Leaderboard: undefined;
  OrderDetail: { id: string };
  // New management screens
  UserManagement: undefined;
  CreateUser: { parentRole: UserRole; childRole: UserRole };
  PricingList: { userId?: string; userName?: string; userRole?: UserRole };
  PricingManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: any[] };
  CreatePricing: { parentId?: string; childRole: UserRole };
  EditPricing: { pricingId: number };
  SchemeManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: any[] };
  CreateScheme: { parentId?: string; childRole: UserRole; selectedUsers?: any[] };
  EditScheme: { schemeId: number; selectedUsers?: any[] };
  OffersManagement: { selectedUsers?: any[] };
  CreateOffer: { selectedUsers?: any[] };
  EditOffer: { offerId: number; selectedUsers?: any[] };
  Announcements: undefined;
};

type TabParamList = {
  Home: undefined;
  Account: undefined;
  More: undefined;
  Management: undefined;
  Orders: undefined;
  Leaderboard: undefined;
  Progress: undefined;
  Announcements: undefined;
};

type TabScreenConfig = {
  name: keyof TabParamList;
  component: React.ComponentType<any>;
  icon: string;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

// Role-based tab configurations
const getTabScreensByRole = (role: UserRole): TabScreenConfig[] => {
  // Common tabs for all roles
  const commonTabs: TabScreenConfig[] = [
    { name: 'Home', component: HomeScreen, icon: 'home' },
  ];

  // Role-specific tabs
  if (role === UserRole.OOGE_TEAM) {
    return [
      ...commonTabs,
      { name: 'Management', component: OogeManagementNavigator, icon: 'business' },
      { name: 'Announcements', component: AnnouncementScreen, icon: 'announcement' },
      { name: 'More', component: MenuScreen, icon: 'more-horiz' },
      { name: 'Account', component: AccountScreen, icon: 'person' },
    ];
  }
  if (role === UserRole.SUPER_STOCKIST) {
    return [
      ...commonTabs,
      { name: 'Management', component: OogeManagementNavigator, icon: 'business' },
      { name: 'Announcements', component: AnnouncementScreen, icon: 'announcement' },
      { name: 'More', component: MenuScreen, icon: 'more-horiz' },
      { name: 'Account', component: AccountScreen, icon: 'person' },
    ];
  }
  if (role === UserRole.DISTRIBUTOR) {
    return [
      ...commonTabs,
      { name: 'Management', component: OogeManagementNavigator, icon: 'business' },
      { name: 'Announcements', component: AnnouncementScreen, icon: 'announcement' },
      { name: 'More', component: MenuScreen, icon: 'more-horiz' },
      { name: 'Account', component: AccountScreen, icon: 'person' },
    ];
  }
  if (role === UserRole.RETAILER) {
    return [
      ...commonTabs,
      { name: 'Progress', component: ProgressScreen, icon: 'trending-up' },
      { name: 'More', component: MenuScreen, icon: 'more-horiz' },
      { name: 'Account', component: AccountScreen, icon: 'person' },
    ];
  }
  // PUBLIC or fallback
  return [...commonTabs];
};

// Role-specific navigator that uses the centralized implementation
function OogeManagementNavigator() {
  return <ManagementNavigator />;
}

function TabNavigator() {
  const { currentUser } = useUser();
  const role = currentUser?.role || UserRole.PUBLIC;
  const tabScreens = getTabScreensByRole(role);

  return (
    <>
    <Header/>
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#6366f1',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarHideOnKeyboard: true,
      }}
    >
      {tabScreens.map((screen) => (
        <Tab.Screen
          key={screen.name}
          name={screen.name}
          component={screen.component}
          options={{
            tabBarIcon: ({ color }) => (
              <Icon name={screen.icon} size={24} color={color} />
            ),
          }}
          />
        ))}
    </Tab.Navigator>
    </>
  );
}

// Stack Screen Configuration
const stackScreens: Array<{
  name: keyof RootStackParamList;
  component: React.ComponentType<any>;
  options: NativeStackNavigationOptions;
}> = [
  { name: 'Splash', component: SplashScreen, options: { headerShown: false } },
  { name: 'Login', component: LoginScreen, options: { headerShown: false } },
  { name: 'MainApp', component: TabNavigator, options: { header: () => false } },
  { name: 'HelpForm', component: HelpForm, options: {headerShown: false }},
  { name: 'ProductDetail', component: ProductDetail, options: { header: () => <Header/> } },
  { name: 'ProductListing', component: ProductListingScreen, options: { header: () => <Header/> } },
  { name: 'RegisterWarranty', component: RegisterWarrantyScreen, options: { headerShown: false } },
  { name: 'WarrantyHistory', component: WarrantyHistoryScreen, options: { headerShown: false } },
  { name: 'WarrantyDetail', component: WarrantyDetailScreen, options: { headerShown: false } },
  { name: 'Schemes', component: Schemes, options: { headerShown: true, headerTitle: 'Schemes', headerTintColor: '#6366f1' } },
  { name: 'Offers', component: OffersScreen, options: { headerShown: true, headerTitle: 'Special Offers', headerTintColor: '#6366f1' } },
  { name: 'Approvals', component: ApprovalsScreen, options: { headerShown: true, headerTitle: 'Pending Approvals', headerTintColor: '#6366f1' } },
  { name: 'Orders', component: OrdersScreen, options: { headerShown: true, headerTitle: 'Orders', headerTintColor: '#6366f1', headerStyle: { backgroundColor: '#ffffff' } } },
  { name: 'Settings', component: SettingsScreen, options: { headerShown: true, headerTitle: 'Settings', headerTintColor: '#6366f1' } },
  { name: 'Cart', component: CartScreen, options: { headerShown: true, headerTitle: 'Shopping Cart', headerTintColor: '#6366f1' } },
  { name: 'BuyNow', component: BuyNowScreen, options: { headerShown: true, headerTitle: 'Checkout', headerTintColor: '#6366f1' } },
  { name: 'ThankYou', component: ThankYouScreen, options: { headerShown: false } },
  { name: 'AddAddress', component: AddAddressScreen, options: { headerShown: false, headerTitle: 'Add Address', headerTintColor: '#6366f1' } },
  { name: 'EditAddress', component: EditAddressScreen, options: { headerShown: false} },
  { name: 'Notifications', component: NotificationScreen, options: { headerShown: false } },

  // Management screens
  { name: 'SuperStockistList', component: SuperStockistListScreen, options: { headerShown: true, headerTitle: 'Super Stockists', headerTintColor: '#6366f1' } },
  { name: 'SuperStockistDetail', component: SuperStockistDetailScreen, options: { headerShown: true, headerTitle: 'Super Stockist Details', headerTintColor: '#6366f1' } },
  { name: 'DistributorList', component: DistributorListScreen, options: { headerShown: true, headerTitle: 'Distributors', headerTintColor: '#6366f1' } },
  { name: 'DistributorDetail', component: DistributorDetailScreen, options: { headerShown: true, headerTitle: 'Distributor Details', headerTintColor: '#6366f1' } },
  { name: 'RetailerList', component: RetailerListScreen, options: { headerShown: true, headerTitle: 'Retailers', headerTintColor: '#6366f1' } },
  { name: 'RetailerDetail', component: RetailerDetailScreen, options: { headerShown: true, headerTitle: 'Retailer Details', headerTintColor: '#6366f1' } },
  { name: 'Leaderboard', component: LeaderboardScreen, options: { headerShown: true, headerTitle: 'Leaderboard', headerTintColor: '#6366f1' } },
  // New management screens
  { name: 'UserManagement', component: UserManagementScreen, options: { headerShown: false } },
  { name: 'CreateUser', component: CreateUserScreen, options: { headerShown: false } },
  { name: 'PricingList', component: PricingListScreen, options: { headerShown: false } },
  { name: 'PricingManagement', component: PricingManagementScreen, options: { headerShown: false } },
  { name: 'CreatePricing', component: CreatePricingScreen, options: { headerShown: false } },
  { name: 'SchemeManagement', component: SchemeManagementScreen, options: { headerShown: false } },
  { name: 'CreateScheme', component: CreateSchemeScreen, options: { headerShown: false } },
  { name: 'EditScheme', component: CreateSchemeScreen, options: { headerShown: false } },
  { name: 'OffersManagement', component: OffersManagementScreen, options: { headerShown: false } },
  { name: 'CreateOffer', component: CreateEditOfferScreen, options: { headerShown: false } },
  { name: 'EditOffer', component: CreateEditOfferScreen, options: { headerShown: false } },
  { name: 'Announcements', component: AnnouncementScreen, options: { headerShown: false } },
];

function AppNavigatorContent() {
  return (
    <Stack.Navigator initialRouteName="Splash">
      {stackScreens.map((screen) => (
        <Stack.Screen
          key={screen.name}
          name={screen.name}
          component={screen.component}
          options={screen.options}
        />
      ))}
    </Stack.Navigator>
  );
}

export default function AppNavigator() {
  return (
    <NavigationContainer ref={navigationRef}>
      <UserProvider>
        <NotificationProvider>
          <AppNavigatorContent />
        </NotificationProvider>
      </UserProvider>
    </NavigationContainer>
  );
}