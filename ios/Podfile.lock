PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - fast_float (6.1.4)
  - FBLazyVector (0.77.1)
  - Firebase (11.13.0):
    - Firebase/Core (= 11.13.0)
  - Firebase/Analytics (11.13.0):
    - Firebase/Core
  - Firebase/Core (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.13.0)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Messaging (11.13.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.13.0)
  - FirebaseAnalytics (11.13.0):
    - FirebaseAnalytics/AdIdSupport (= 11.13.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - fmt (11.0.2)
  - glog (0.3.5)
  - GoogleAppMeasurement (11.13.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.13.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.13.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities (8.1.0):
    - GoogleUtilities/AppDelegateSwizzler (= 8.1.0)
    - GoogleUtilities/Environment (= 8.1.0)
    - GoogleUtilities/Logger (= 8.1.0)
    - GoogleUtilities/MethodSwizzler (= 8.1.0)
    - GoogleUtilities/Network (= 8.1.0)
    - "GoogleUtilities/NSData+zlib (= 8.1.0)"
    - GoogleUtilities/Privacy (= 8.1.0)
    - GoogleUtilities/Reachability (= 8.1.0)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.1.0)
    - GoogleUtilities/UserDefaults (= 8.1.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.1.0):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.77.1):
    - hermes-engine/Pre-built (= 0.77.1)
  - hermes-engine/Pre-built (0.77.1)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.77.1)
  - RCTRequired (0.77.1)
  - RCTTypeSafety (0.77.1):
    - FBLazyVector (= 0.77.1)
    - RCTRequired (= 0.77.1)
    - React-Core (= 0.77.1)
  - React (0.77.1):
    - React-Core (= 0.77.1)
    - React-Core/DevSupport (= 0.77.1)
    - React-Core/RCTWebSocket (= 0.77.1)
    - React-RCTActionSheet (= 0.77.1)
    - React-RCTAnimation (= 0.77.1)
    - React-RCTBlob (= 0.77.1)
    - React-RCTImage (= 0.77.1)
    - React-RCTLinking (= 0.77.1)
    - React-RCTNetwork (= 0.77.1)
    - React-RCTSettings (= 0.77.1)
    - React-RCTText (= 0.77.1)
    - React-RCTVibration (= 0.77.1)
  - React-callinvoker (0.77.1)
  - React-Core (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.77.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.77.1)
    - React-Core/RCTWebSocket (= 0.77.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.77.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety (= 0.77.1)
    - React-Core/CoreModulesHeaders (= 0.77.1)
    - React-jsi (= 0.77.1)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.77.1)
    - ReactCommon
    - SocketRocket (= 0.7.1)
  - React-cxxreact (0.77.1):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.77.1)
    - React-debug (= 0.77.1)
    - React-jsi (= 0.77.1)
    - React-jsinspector
    - React-logger (= 0.77.1)
    - React-perflogger (= 0.77.1)
    - React-runtimeexecutor (= 0.77.1)
    - React-timing (= 0.77.1)
  - React-debug (0.77.1)
  - React-defaultsnativemodule (0.77.1):
    - hermes-engine
    - RCT-Folly
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
  - React-domnativemodule (0.77.1):
    - hermes-engine
    - RCT-Folly
    - React-Fabric
    - React-FabricComponents
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.77.1)
    - React-Fabric/attributedstring (= 0.77.1)
    - React-Fabric/componentregistry (= 0.77.1)
    - React-Fabric/componentregistrynative (= 0.77.1)
    - React-Fabric/components (= 0.77.1)
    - React-Fabric/core (= 0.77.1)
    - React-Fabric/dom (= 0.77.1)
    - React-Fabric/imagemanager (= 0.77.1)
    - React-Fabric/leakchecker (= 0.77.1)
    - React-Fabric/mounting (= 0.77.1)
    - React-Fabric/observers (= 0.77.1)
    - React-Fabric/scheduler (= 0.77.1)
    - React-Fabric/telemetry (= 0.77.1)
    - React-Fabric/templateprocessor (= 0.77.1)
    - React-Fabric/uimanager (= 0.77.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.77.1)
    - React-Fabric/components/root (= 0.77.1)
    - React-Fabric/components/view (= 0.77.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.77.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.77.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.77.1)
    - React-FabricComponents/textlayoutmanager (= 0.77.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.77.1)
    - React-FabricComponents/components/iostextinput (= 0.77.1)
    - React-FabricComponents/components/modal (= 0.77.1)
    - React-FabricComponents/components/rncore (= 0.77.1)
    - React-FabricComponents/components/safeareaview (= 0.77.1)
    - React-FabricComponents/components/scrollview (= 0.77.1)
    - React-FabricComponents/components/text (= 0.77.1)
    - React-FabricComponents/components/textinput (= 0.77.1)
    - React-FabricComponents/components/unimplementedview (= 0.77.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired (= 0.77.1)
    - RCTTypeSafety (= 0.77.1)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.77.1)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.77.1)
  - React-featureflagsnativemodule (0.77.1):
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - React-graphics (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.77.1)
    - React-jsi
    - React-jsiexecutor (= 0.77.1)
    - React-jsinspector
    - React-perflogger (= 0.77.1)
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.77.1):
    - hermes-engine
    - RCT-Folly
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-ImageManager (0.77.1):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
  - React-jsi (0.77.1):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
  - React-jsiexecutor (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.77.1)
    - React-jsi (= 0.77.1)
    - React-jsinspector
    - React-perflogger (= 0.77.1)
  - React-jsinspector (0.77.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-featureflags
    - React-jsi
    - React-perflogger (= 0.77.1)
    - React-runtimeexecutor (= 0.77.1)
  - React-jsitracing (0.77.1):
    - React-jsi
  - React-logger (0.77.1):
    - glog
  - React-Mapbuffer (0.77.1):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.77.1):
    - hermes-engine
    - RCT-Folly
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - react-native-document-picker (9.3.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-image-picker (8.2.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-maps (1.23.8):
    - react-native-maps/Maps (= 1.23.8)
  - react-native-maps/Generated (1.23.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-maps/Maps (1.23.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-maps/Generated
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common (= 5.4.0)
    - react-native-safe-area-context/fabric (= 5.4.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/common (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/fabric (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-slider (4.5.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-slider/common (= 4.5.6)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-slider/common (4.5.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-video (6.14.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-video/Video (= 6.14.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-video/Fabric (6.14.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-video/Video (6.14.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-video/Fabric
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.77.1)
  - React-NativeModulesApple (0.77.1):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.77.1):
    - DoubleConversion
    - RCT-Folly (= 2024.11.18.00)
  - React-performancetimeline (0.77.1):
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact
    - React-featureflags
    - React-timing
  - React-RCTActionSheet (0.77.1):
    - React-Core/RCTActionSheetHeaders (= 0.77.1)
  - React-RCTAnimation (0.77.1):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTAppDelegate (0.77.1):
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTFBReactNativeSpec (0.77.1):
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTImage (0.77.1):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.77.1):
    - React-Core/RCTLinkingHeaders (= 0.77.1)
    - React-jsi (= 0.77.1)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.77.1)
  - React-RCTNetwork (0.77.1):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTSettings (0.77.1):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTText (0.77.1):
    - React-Core/RCTTextHeaders (= 0.77.1)
    - Yoga
  - React-RCTVibration (0.77.1):
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-rendererconsistency (0.77.1)
  - React-rendererdebug (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
  - React-rncore (0.77.1)
  - React-RuntimeApple (0.77.1):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.77.1):
    - React-jsi (= 0.77.1)
  - React-RuntimeHermes (0.77.1):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.77.1)
  - React-utils (0.77.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
    - React-jsi (= 0.77.1)
  - ReactAppDependencyProvider (0.77.1):
    - ReactCodegen
  - ReactCodegen (0.77.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.77.1):
    - ReactCommon/turbomodule (= 0.77.1)
  - ReactCommon/turbomodule (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.77.1)
    - React-cxxreact (= 0.77.1)
    - React-jsi (= 0.77.1)
    - React-logger (= 0.77.1)
    - React-perflogger (= 0.77.1)
    - ReactCommon/turbomodule/bridging (= 0.77.1)
    - ReactCommon/turbomodule/core (= 0.77.1)
  - ReactCommon/turbomodule/bridging (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.77.1)
    - React-cxxreact (= 0.77.1)
    - React-jsi (= 0.77.1)
    - React-logger (= 0.77.1)
    - React-perflogger (= 0.77.1)
  - ReactCommon/turbomodule/core (0.77.1):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.77.1)
    - React-cxxreact (= 0.77.1)
    - React-debug (= 0.77.1)
    - React-featureflags (= 0.77.1)
    - React-jsi (= 0.77.1)
    - React-logger (= 0.77.1)
    - React-perflogger (= 0.77.1)
    - React-utils (= 0.77.1)
  - RNCAsyncStorage (2.1.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNCPicker (2.11.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNDateTimePicker (8.3.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNFBAnalytics (22.2.1):
    - Firebase/Analytics (= 11.13.0)
    - React-Core
    - RNFBApp
  - RNFBApp (22.2.1):
    - Firebase/CoreOnly (= 11.13.0)
    - React-Core
  - RNFBMessaging (22.2.1):
    - Firebase/Messaging (= 11.13.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.25.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated (3.17.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.17.4)
    - RNReanimated/worklets (= 3.17.4)
    - Yoga
  - RNReanimated/reanimated (3.17.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.17.4)
    - Yoga
  - RNReanimated/reanimated/apple (3.17.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.17.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/worklets/apple (= 3.17.4)
    - Yoga
  - RNReanimated/worklets/apple (3.17.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.10.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.10.0)
    - Yoga
  - RNScreens/common (4.10.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.11.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.11.2)
    - Yoga
  - RNSVG/common (15.11.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNVectorIcons (10.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - Firebase
  - FirebaseCore
  - FirebaseCoreInternal
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-video (from `../node_modules/react-native-video`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-25-RNv0.77.0-d4f25d534ab744866448b36ca3bf3d97c08e638c
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-video:
    :path: "../node_modules/react-native-video"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBLazyVector: 79c4b7ec726447eec5f8593379466bd9fde1aa14
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  FirebaseAnalytics: 630349facf4a114a0977e5d7570e104261973287
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreExtension: c048485c347616dba6165358dbef765c5197597b
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseMessaging: 195bbdb73e6ca1dbc76cd46e73f3552c084ef6e4
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: eb93e2f488219332457c3c4eafd2738ddc7e80b8
  GoogleAppMeasurement: 0dfca1a4b534d123de3945e28f77869d10d0d600
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  hermes-engine: ccc24d29d650ea725d582a9a53d57cd417fbdb53
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: e78785aa9ba2ed998ea4151e314036f6c49e6d82
  RCTDeprecation: 664055db806cce35c3c1b43c84414dd66e117ae6
  RCTRequired: dc9a83fa1012054f94430d210337ca3a1afe6fc0
  RCTTypeSafety: 031cefa254a1df313a196f105b8fcffdab1c5ab6
  React: 8edfc46c315852ec88ea4a29d5e79019af3dc667
  React-callinvoker: 4450b01574dfc7a8f074f7e29e6965ac04859c8f
  React-Core: adee73236280f8708e8973b8cbc60c834e591ecb
  React-CoreModules: 78e04d2319b1b61e0d4ed7fcd3e366d461819279
  React-cxxreact: 05d4cfc36a33f309f280753995bf77eb5f12b40e
  React-debug: b0f7271aeacc2eb9e34f863397dcfc204ef721c0
  React-defaultsnativemodule: 9ce2a0afe47f3b724f8adec28de3712d897a069a
  React-domnativemodule: 23a99da612d4a41f05f7c0f9665bd557638f3a99
  React-Fabric: 189561e6cd72aacbd6a1bc92fa98b12ae2717d2b
  React-FabricComponents: 32650e154e3958fedd1de88a94cef27e52288d7e
  React-FabricImage: 5e81e0fae1817eec1840408be77c7e6ba3e2ee98
  React-featureflags: 23d3dcdac6c9badeeb631db8a0883c7a3108d580
  React-featureflagsnativemodule: f374752cb62a577a3bca18d01d1c80dcaeb29299
  React-graphics: 348400b8ba57611d552af6db5dc7d42ccf132d08
  React-hermes: daf648f84569e9cb3d563dd806f09cf14635a356
  React-idlecallbacksnativemodule: 97d2eb4935fa459d2f1127ce594c3b1959b0828d
  React-ImageManager: ee8526b1af93152133709104c6d649d5dada63b3
  React-jserrorhandler: 17774783cd8d1377d3b23efbd4af4e80a2bca065
  React-jsi: 2b9e6349efb0cd61b871dcd6db126fb13e1e6488
  React-jsiexecutor: 8050076ff38e95a6852c5af0f516cf05889a3737
  React-jsinspector: 55187c59747d78d14dae0d301beef22559099348
  React-jsitracing: 9e7066f99151f99ed588f2055e011845b12a1bf6
  React-logger: e7eeebaed32b88dcc29b10901aa8c5822dc397c4
  React-Mapbuffer: 73dd1210c4ecf0dfb4e2d4e06f2a13f824a801a9
  React-microtasksnativemodule: d03753688e2abf135edcd4160ab3ce7526da8b0d
  react-native-document-picker: bfe9c7d72908d2faba56a4765373c6367090ae2e
  react-native-image-picker: 84b261e109faa7312bca59f1f65c8f2b22865496
  react-native-maps: 261ba9a4e4a2bbe79940f06345bab59c357d3df2
  react-native-safe-area-context: afcc2e2b3e78ae8ef90d81e658aacee34ebc27ea
  react-native-slider: bb7eb4732940fab78217e1c096bb647d8b0d1cf3
  react-native-video: bf7317b3951c35adc5860561a1b6b3ddf0e50af4
  React-nativeconfig: cb207ebba7cafce30657c7ad9f1587a8f32e4564
  React-NativeModulesApple: 8411d548b1ad9d2b3e597beb9348e715c8020e0c
  React-perflogger: c4c3b7c18f8a50cdbe2bcdd2f15705ba029a5a02
  React-performancetimeline: 38bda258bd9f9da19b27615e8edfbec064aa42cc
  React-RCTActionSheet: 0fdf55fb8724856d63ca8c63cdb4e2325e15e8ec
  React-RCTAnimation: b2fcc7c462f1fb5e195a5547f6e405ec9a60d80f
  React-RCTAppDelegate: d5aed095faa6fd0b0aff3c98d0b078680890cde2
  React-RCTBlob: 3b5441953e3dcc4aaee8f539b17d9c54b7a4b111
  React-RCTFabric: 022ff67d55ac5833b984085628e15af113cd9b52
  React-RCTFBReactNativeSpec: 536442edc77efaec464c3c805c1e44fd811639d3
  React-RCTImage: f189ae651e3c97879b4cdefcba1d4cffe55439da
  React-RCTLinking: 759ac5e4aed95ac3c29849f98ff3f3b5ece830ed
  React-RCTNetwork: ce1f38434a70eb1e228344f7632e636c3ceca03b
  React-RCTSettings: 3602ea3adf9009f6d09461bf05f7e392414c32d8
  React-RCTText: e48b4b54eab3f4cfea9be1228b5ef9ad3b8172c1
  React-RCTVibration: 2e4dc335dd1e57c7004bcc07e7f5319e5968d5cf
  React-rendererconsistency: c766ce7261ab6ed6be7bc155c403e29436d4f156
  React-rendererdebug: f8bf864b2646944c3f7c41555dbed0b5d7aea5d1
  React-rncore: cafe45e14d870bbecbbf4bd89e12ef3b596e1f2d
  React-RuntimeApple: d3f5e05cfd5e212077a2e8dbdcf051ee237273b4
  React-RuntimeCore: 3c513c4cad66a889614fc4b70fadacdf900f0c7a
  React-runtimeexecutor: 201311bdafb53b5c30292782c8ee90193af86d91
  React-RuntimeHermes: 25194897d244f2c1b68511926c7be413466f6e6c
  React-runtimescheduler: b2839d0c1276b8f0edabc28414c9a5c82bb5c700
  React-timing: 127d8598b5a15ae5b29ebd0ec474d590285c6f2f
  React-utils: 238c18f8035ace0faccd7e8ce574ccfc7adf26aa
  ReactAppDependencyProvider: 41e9fb63606c32cce924653d2d410cb01ec81286
  ReactCodegen: d9a09a7f7eee93f54d0b4135d5ca66b31b0c42a5
  ReactCommon: 08f4808f02ff115884e870e5cfea689703ff759a
  RNCAsyncStorage: 62fdbd7e63312228d4868779026248c2677911e1
  RNCPicker: cfb51a08c6e10357d9a65832e791825b0747b483
  RNDateTimePicker: e30677c15dfba25452b1434181970162115cc4fe
  RNFBAnalytics: 4a92063adff06d850b26f2aeb59a0b5b575b0f21
  RNFBApp: 557f15b87d602ed5879a1cbd6dc574481bccb278
  RNFBMessaging: e6480aaf434826565d5a40b01c422fb0f58fc8a9
  RNGestureHandler: dcb1b1db024f3744b03af56d132f4f72c4c27195
  RNReanimated: ff2d357b213ca4a2adc48c1548686da18cd32da8
  RNScreens: 790123c4a28783d80a342ce42e8c7381bed62db1
  RNSVG: 877cd57c2db63dc9f1fd1bbb5e95506ccd481df6
  RNVectorIcons: bd818296a51dc2bb8c3bd97a3ca399df1afe216d
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: 1fd059161b449018342943b095a6d4e69bcaa719

PODFILE CHECKSUM: 36fee86cf4c9477e9d44fbc6fe8c789aecd5359d

COCOAPODS: 1.16.2
