import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Dimensions, ScrollView } from 'react-native';
import MapView, { Marker } from 'react-native-maps';

interface ApprovalItem {
  id: string;
  companyName: string;
  gstin: string;
}

interface ApprovalCardProps {
  approvals: ApprovalItem[];
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
}

const ApprovalCard: React.FC<ApprovalCardProps> = ({ 
  approvals,
  onApprove, 
  onReject 
}) => {
  const [showAllApprovals, setShowAllApprovals] = useState(false);
  const INITIAL_DISPLAY_COUNT = 2;

  const displayedApprovals = showAllApprovals 
    ? approvals 
    : approvals.slice(0, INITIAL_DISPLAY_COUNT);

  const renderApprovalCard = (approval: ApprovalItem) => (
    <View key={approval.id} className="bg-white rounded-lg p-4 shadow mb-4">
      <Text className="text-lg font-bold mb-2">Connection Pending Approval</Text>
      
      <View className="flex-row">
        {/* Left side - Map */}
        <View className="flex-1 mr-2">
          {/* <MapView
            style={{ width: Dimensions.get('window').width / 2 - 20, height: 150 }}
            initialRegion={{
              latitude: approval.location.latitude,
              longitude: approval.location.longitude,
              latitudeDelta: 0.0922,
              longitudeDelta: 0.0421,
            }}
          >
            <Marker
              // coordinate={{
              //   latitude: approval.location.latitude,
              //   longitude: approval.location.longitude,
              // }}
              title={approval.companyName}
            />
          </MapView> */}
        </View>
        
        {/* Right side - Content */}
        <View className="flex-1 ml-2">
          <View className="border p-3 rounded-lg flex-1">
            <Text className="font-semibold text-base">{approval.companyName}</Text>
            <Text className="text-gray-500 text-sm mb-2">GSTIN: {approval.gstin}</Text>
            
            <View className="mt-auto">
              <TouchableOpacity 
                className="bg-accent px-4 py-2 rounded mb-2" 
                onPress={() => onApprove(approval.id)}
              >
                <Text className="text-white font-bold text-center">Approve</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                className="bg-danger px-4 py-2 rounded" 
                onPress={() => onReject(approval.id)}
              >
                <Text className="text-white font-bold text-center">Reject</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <View>
      {displayedApprovals.map(renderApprovalCard)}
      
      {approvals.length > INITIAL_DISPLAY_COUNT && (
        <TouchableOpacity 
          onPress={() => setShowAllApprovals(!showAllApprovals)}
          className="bg-white px-4 py-3 rounded-lg mt-2 border border-gray-200"
        >
          <Text className="text-primary text-center font-semibold">
            {showAllApprovals 
              ? 'Show Less' 
              : `Show More (${approvals.length - INITIAL_DISPLAY_COUNT})`
            }
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ApprovalCard;