import { View, Text, ActivityIndicator, Alert } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Subscription, PaymentMethod, Invoice } from '../../types';

type BillingSectionProps = {
  subscription: Subscription | null;
  paymentMethods: PaymentMethod[];
  invoices: Invoice[];
  loading: boolean;
  error: string | null;
};

export default function BillingSection({
  subscription,
  paymentMethods,
  invoices,
  loading,
  error,
}: BillingSectionProps) {
  if (loading) {
    return (
      <View className="p-4">
        <ActivityIndicator size="large" color="#6366f1" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="p-4 bg-red-50 rounded-lg mt-4">
        <Text className="text-red-600">{error}</Text>
      </View>
    );
  }

  return (
    <View className="bg-white rounded-lg shadow mt-4 p-4">
      {/* Subscription Status */}
      <View className="pb-4 border-b border-gray-100">
        <View className="flex-row items-center justify-between mb-2">
          <Text className="text-lg font-semibold">Subscription Plan</Text>
          <View className="flex-row items-center">
            <Icon 
              name={subscription?.status === 'active' ? 'check-circle' : 'error'} 
              color={subscription?.status === 'active' ? '#10b981' : '#ef4444'} 
              size={20} 
            />
            <Text className={`ml-2 ${subscription?.status === 'active' ? 'text-success' : 'text-danger'}`}>
              {subscription?.planName}
            </Text>
          </View>
        </View>
        <Text className="text-gray-500">
          Next billing date: {subscription?.nextBillingDate}
        </Text>
      </View>

      {/* Payment Method */}
      <View className="py-4 border-b border-gray-100">
        <Text className="text-lg font-semibold mb-2">Payment Method</Text>
        {paymentMethods.map((method) => (
          <View key={method.id} className="flex-row items-center">
            <Icon name="credit-card" size={20} color="#6366f1" />
            <Text className="ml-2">
              **** **** **** {method.last4} ({method.brand})
            </Text>
          </View>
        ))}
      </View>

      {/* Recent Invoices */}
      <View className="pt-4">
        <Text className="text-lg font-semibold mb-2">Recent Invoices</Text>
        {invoices.map((invoice) => (
          <View key={invoice.id} className="flex-row justify-between items-center py-2">
            <View>
              <Text className="font-medium">{invoice.date}</Text>
              <Text className="text-gray-500">#{invoice.id}</Text>
            </View>
            <Text className={`font-semibold ${invoice.status === 'paid' ? 'text-success' : 'text-danger'}`}>
              ${invoice.amount.toFixed(2)}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
}