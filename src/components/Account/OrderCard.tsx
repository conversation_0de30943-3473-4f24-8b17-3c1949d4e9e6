import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface OrderCardProps {
  orderNumber: number;
  items: string[];
  date: string;
  total: string;
  status?: string;
  onCallBuyer: () => void;
  onMarkShipped: () => void;
}

const OrderCard: React.FC<OrderCardProps> = ({
  orderNumber,
  items,
  date,
  total,
  status,
  onCallBuyer,
  onMarkShipped,
}) => {
  const getStatusColor = (status?: string) => {
    switch (status?.toUpperCase()) {
      case 'PENDING':
        return 'bg-yellow-50 text-yellow-600';
      case 'CONFIRMED':
        return 'bg-green-50 text-green-600';
      case 'REJECTED':
        return 'bg-red-50 text-red-600';
      default:
        return 'bg-gray-50 text-gray-600';
    }
  };

  return (
    <View className="bg-white p-4 mb-3 rounded-lg border border-gray-100">
      <View className="flex-row justify-between items-start mb-3">
        <View className="flex-1">
          <Text className="text-lg font-bold text-gray-800 mb-1">
            Order #{orderNumber}
          </Text>
          <Text className="text-gray-600 text-sm mb-2">{date}</Text>
          <Text className="text-lg font-semibold text-green-600">
            {total}
          </Text>
        </View>
        
        {status && (
          <View className={`px-3 py-1 rounded-full ${getStatusColor(status)}`}>
            <Text className={`text-xs font-semibold ${getStatusColor(status).split(' ')[1]}`}>
              {status}
            </Text>
          </View>
        )}
      </View>

      <View className="mb-3">
        <Text className="text-gray-700 font-medium mb-1">Items:</Text>
        {items.map((item, index) => (
          <Text key={index} className="text-gray-600 text-sm ml-2">
            • {item}
          </Text>
        ))}
      </View>

      <View className="flex-row space-x-3">
        <TouchableOpacity
          className="flex-1 bg-blue-50 py-2 rounded-lg flex-row items-center justify-center"
          onPress={onCallBuyer}
        >
          <Icon name="phone" size={16} color="#3b82f6" />
          <Text className="text-blue-600 font-medium ml-1">Call Buyer</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="flex-1 bg-green-50 py-2 rounded-lg flex-row items-center justify-center"
          onPress={onMarkShipped}
        >
          <Icon name="local-shipping" size={16} color="#10b981" />
          <Text className="text-green-600 font-medium ml-1">Mark Shipped</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default OrderCard;
