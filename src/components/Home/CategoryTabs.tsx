import React, { useState } from 'react';
import { View, Text, ScrollView, Image, TouchableOpacity, StyleSheet, Dimensions, ActivityIndicator } from "react-native";
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useGetCategoriesQuery } from '../../services/api/apiSlice';
import CardSkeleton from '../common/skeletons/CardSkeleton';

interface Category {
  id: number;
  name: string;
  urls: string[];
  description: string;
}

type RootStackParamList = {
  ProductListing: {
    category: string
    categoryId: number
  };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'ProductListing'>;

// const category = categories;
// const  category = categories;

export const colors = [
  '#fef3c7', // Light Yellow
  '#dbeafe', // Light Blue
  '#f3e8ff', // Light Purple
  '#dcfce7', // Light Green
  '#fee2e2', // Light Red
  '#fae8ff'  // Light Pink
];


const CategoryTabs: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const screenWidth = Dimensions.get('window').width;
  const itemWidth = (screenWidth - 64) / 3; // Show 3 items per row with padding

  // Use RTK Query hook to fetch categories
  const {
    data,
    isLoading,
    error
  } = useGetCategoriesQuery({ page: 0, count: 100, type: 1, status: 1 });

  // Get categories from the response
  const categories: Category[] = data || [];

  // State for active tab
  const [activeTab, setActiveTab] = useState(
    categories.length > 0 ? categories[0]?.name : 'Neckband'
  );

  const handleCategoryPress = (category: string, categoryId: number) => {
    setActiveTab(category);

    // Navigate to the ProductListing screen with the selected category
    navigation.navigate('ProductListing', { category, categoryId });
  };

  if (error) {
    return (
      <View className='flex-1 justify-center items-center'>
        <Text className='text-red-500'>Failed to load categories</Text>
      </View>
    );
  }

    const CategorySkeleton = () => (
    <View className="p-4">
      <View style={{ flexDirection: 'row' }}>
        {[1, 2, 3].map((item) => (
          <View key={item} className="w-1/3 p-2">
            <CardSkeleton height={120} />
          </View>
        ))}
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <CategorySkeleton/>
    );
  }

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      {categories.map((category, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => handleCategoryPress(category.name, category.id)}
          style={[
            styles.tabItem,
            { width: itemWidth, backgroundColor: colors[index % colors.length] },
            activeTab === category.name && styles.activeTab
          ]}
        >
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: category?.urls[0] || 'https://m.media-amazon.com/images/I/61iHi7VwQJL.jpg' }}
              style={styles.categoryImage}
              resizeMode="contain"
            />
          </View>
          <Text style={[
            styles.categoryText,
            activeTab === category.name && styles.activeText
          ]}>
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContent: {
    paddingHorizontal: 16,
    gap: 12,
    paddingVertical: 8,
  },
  tabItem: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 16,
    marginBottom: 8,
  },
  activeTab: {
    transform: [{ scale: 1.05 }],
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3.84,
    elevation: 5,
  },
  imageContainer: {
    backgroundColor: 'white',
    padding: 8,
    borderRadius: 12,
    marginBottom: 8,
  },
  categoryImage: {
    width: 60,
    height: 60,
  },
  categoryText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#374151',
  },
  activeText: {
    color: '#4f46e5',
  }
});

export default CategoryTabs;

