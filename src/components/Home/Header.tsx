
import React, { useCallback } from 'react';
import { View, Image, TouchableOpacity, Text, Alert } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import SearchBar from './SearchBar';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import { useSelector, useDispatch } from 'react-redux';
import type { RootState } from '../../redux/store';
import { useCartSync } from '../../hooks/useCartSync';
import { apiSlice } from '../../services/api/apiSlice';
import { bannerApi } from './api/bannerslice';
import { cartApi } from '../../screens/cart/cartApi/apiSlice';
import { PromotionalApi } from './api/Promotionalslice';
import { managementApi } from '../../screens/management/api/apiSlice';
import { offersApi } from '../../screens/management/api/offersApi';
import { schemeApi } from '../../screens/management/api/scheme';

const Header = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const { currentUser, logout } = useUser();
  
  // Use the cart sync hook
  const { refetchCart } = useCartSync();
  
  // Get cart count from Redux (single source of truth)
  const cartCount = useSelector((state: RootState) => state.cart.items.length);

  useFocusEffect(
    useCallback(() => {
      if (currentUser?.id) {
        console.log('🔄 [HEADER] Screen focused, refetching cart');
        refetchCart();
      }
    }, [currentUser?.id, refetchCart])
  );

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();

            dispatch(apiSlice.util.resetApiState());
            dispatch(bannerApi.util.resetApiState());
            dispatch(cartApi.util.resetApiState());
            dispatch(PromotionalApi.util.resetApiState());
            dispatch(managementApi.util.resetApiState());
            dispatch(offersApi.util.resetApiState());
            dispatch(schemeApi.util.resetApiState());

            navigation.reset({
              index: 0,
              routes: [{ name: 'MainApp' }],
            });
          },
        },
      ]
    );
  };

  console.log('🏠 [HEADER] Cart count from Redux:', cartCount);

  return (
    <View className="bg-white px-4 py-3 shadow-sm">
      <View className="flex-row items-center justify-between mb-3">
        <TouchableOpacity onPress={() => navigation.navigate('Home')}>
          <Image
            source={require('../../assets/ogge_logo.png')}
            className="w-32 h-10"
            resizeMode="cover"
          />
        </TouchableOpacity>

        <View className="flex-row">
          {!currentUser || currentUser.role === UserRole.PUBLIC ? (
            <>
              <TouchableOpacity
                onPress={() => navigation.navigate('HelpForm')}
                className="bg-amber-500 rounded-full px-4 py-1.5 flex-row items-center mr-2 shadow-sm"
              >
                <Icon name="help-box" size={16} color="white" />
                <Text className="text-white text-sm font-semibold ml-1.5">Need help</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigation.navigate('Login')}
                className="bg-indigo-600 rounded-full px-3 py-1 flex-row items-center"
              >
                <Icon name="login" size={16} color="white" />
                <Text className="text-white text-sm font-medium ml-1">Login</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              <TouchableOpacity
                onPress={() => navigation.navigate('Notifications')}
                className="w-10 h-10 items-center justify-center relative"
              >
                <Icon name="bell-outline" size={24} color="#6366f1" />
              </TouchableOpacity>

              {currentUser.role !== UserRole.OOGE_TEAM && (
                <TouchableOpacity
                  onPress={() => navigation.navigate('Cart')}
                  className="w-10 h-10 items-center justify-center relative"
                >
                  <Icon name="shopping-outline" size={24} color="#6366f1" />
                  {cartCount > 0 && (
                    <View className="absolute -top-[0.5px] -right-[1.5px] bg-red-500 rounded-full w-4 h-4 items-center justify-center">
                      <Text className="text-white text-xs">{cartCount}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}

              <TouchableOpacity
                onPress={handleLogout}
                className="w-10 h-10 items-center justify-center relative"
              >
                <Icon name="logout" size={24} color="#6366f1" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      <SearchBar />
    </View>
  );
};

export default Header;
