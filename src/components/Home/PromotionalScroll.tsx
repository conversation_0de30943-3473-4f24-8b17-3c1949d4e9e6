import { View, Text, Image, Dimensions, TouchableOpacity, Animated, FlatList, ActivityIndicator, StyleSheet } from "react-native";
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useRef, useState, useEffect } from 'react';
import { useGetOffersQuery } from "./api/Promotionalslice";
import CardSkeleton from "../common/skeletons/CardSkeleton";

type RootStackParamList = {
  Schemes: undefined;
  Offers: undefined;
};

const CARD_COLORS = [
  { bg: "bg-rose-50", text: "text-rose-600", iconBg: "bg-rose-100" },
  { bg: "bg-emerald-50", text: "text-emerald-600", iconBg: "bg-emerald-100" },
  { bg: "bg-sky-50", text: "text-sky-600", iconBg: "bg-sky-100" },
  { bg: "bg-purple-50", text: "text-purple-600", iconBg: "bg-purple-100" },
  { bg: "bg-amber-50", text: "text-amber-600", iconBg: "bg-amber-100" }
];

const SCHEME_CARD = {
  id: 'scheme-card',
  title: 'Schemes',
  description: 'Explore our latest schemes and offers',
  imageUrl: 'https://study.com/cimages/multimages/16/pyramid-gb5c726dfd_6406686427392629431824.png',
  category: 'Schemes',
  type: 'scheme',
};

const PromotionalScroll = () => {
  const navigation = useNavigation<any>();
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = screenWidth * 0.75;
  const scrollX = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Fetch offers from API
  const { data: apiResponse, isLoading, error } = useGetOffersQuery({
    status: 1,
    page: 0,
    size: 20
  });

  // Process promotions data with cycling colors and inject scheme card at index 1
  const processPromotions = () => {
    const apiPromotions = (apiResponse || []).map((item, index) => ({
      ...item,
      ...CARD_COLORS[index % CARD_COLORS.length],
      type: 'offer'
    }));

    // Create a new array with scheme card inserted at index 1
    const promotionsWithScheme = [];
    
    // Add first item if it exists
    if (apiPromotions.length > 0) {
      promotionsWithScheme.push(apiPromotions[0]);
    }
    
    // Add scheme card at index 1 with appropriate color
    const schemeCardWithColor = {
      ...SCHEME_CARD,
      ...CARD_COLORS[1 % CARD_COLORS.length]
    };
    promotionsWithScheme.push(schemeCardWithColor);
    
    // Add remaining items starting from index 1
    for (let i = 1; i < apiPromotions.length; i++) {
      promotionsWithScheme.push({
        ...apiPromotions[i],
        ...CARD_COLORS[(i + 1) % CARD_COLORS.length] // Adjust color index to account for inserted scheme card
      });
    }

    return promotionsWithScheme;
  };

  const promotions = processPromotions();

  // For infinite scroll effect
  const extendedPromotions = [...promotions, ...promotions, ...promotions];
  const initialScrollIndex = promotions.length;

  const handlePress = (title: string, id: number | string, category: string, type?: string) => {
    if (type === 'scheme' || title === "Schemes" || category === "Schemes") {
      navigation.navigate('Schemes');
    } else {
      navigation.navigate('Offers');
    }
  };

  // Auto scroll functionality
  useEffect(() => {
    if (!promotions.length) return;
    const autoScrollTimer = setInterval(() => {
      if (flatListRef.current) {
        const nextIndex = (currentIndex + 1) % promotions.length;
        flatListRef.current.scrollToIndex({
          index: initialScrollIndex + nextIndex,
          animated: true,
        });
        setCurrentIndex(nextIndex);
      }
    }, 3000);
    return () => clearInterval(autoScrollTimer);
  }, [currentIndex, promotions.length]);

  // Handle scroll end for infinite loop
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { x: scrollX } } }],
    { useNativeDriver: false }
  );

  const handleMomentumScrollEnd = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / cardWidth);
    const actualIndex = index % promotions.length;
    setCurrentIndex(actualIndex);
    
    if (index < promotions.length) {
      flatListRef.current?.scrollToIndex({
        index: index + promotions.length,
        animated: false,
      });
    } else if (index >= promotions.length * 2) {
      flatListRef.current?.scrollToIndex({
        index: index - promotions.length,
        animated: false,
      });
    }
  };

  // Render promotional card with NativeWind styling
  const renderItem = ({ item, index }: { item: any, index: number }) => (
    <TouchableOpacity
      key={`${item.id}-${index}`}
      onPress={() => handlePress(item.title, item.id, item.category || item.title, item.type)}
      className={`${item.bg} rounded-2xl overflow-hidden shadow-sm mx-2`}
      style={{
        width: cardWidth,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 2,
      }}
      activeOpacity={0.9}
    >
      <View className="flex-row items-center p-5">
        <View className="flex-1 pr-4">
          <Text
            className={`${item.text} text-2xl font-bold mb-2`}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {item.title}
          </Text>
          <Text
            className="text-gray-500 text-base mb-4"
            numberOfLines={3}
            ellipsizeMode="tail"
          >
            {item.description || item.region || item.city || ""}
          </Text>
          <View className={`${item.iconBg} self-start rounded-full px-4 py-2`}>
            <Text className={`${item.text} font-semibold`}>
              {item.type === 'scheme' ? 'View Schemes →' : 'Shop Now →'}
            </Text>
          </View>
        </View>
        <View className={`${item.iconBg} w-28 h-28 rounded-full justify-center items-center`}>
          <Image
            source={{ uri: item.imageUrl || item.image }}
            className="w-20 h-20 rounded-full"
            resizeMode="contain"
          />
        </View>
      </View>
    </TouchableOpacity>
  );

  // Render animated dot indicators
  const renderDotIndicators = () => {
    if (!promotions.length) return null;
    
    return (
      <View className="flex-row justify-center mt-4 mb-2">
        {promotions.map((item, index) => {
          const inputRange = [
            (initialScrollIndex + index - 1) * cardWidth,
            (initialScrollIndex + index) * cardWidth,
            (initialScrollIndex + index + 1) * cardWidth,
          ];
          
          const dotWidth = scrollX.interpolate({
            inputRange,
            outputRange: [8, 24, 8],
            extrapolate: 'clamp',
          });
          
          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.4, 1, 0.4],
            extrapolate: 'clamp',
          });
          
          const backgroundColor = scrollX.interpolate({
            inputRange,
            outputRange: [
              'rgb(203, 213, 225)',
              item.text.split('-')[1] === 'rose' ? 'rgb(225, 29, 72)' :
              item.text.split('-')[1] === 'emerald' ? 'rgb(21, 128, 61)' :
              item.text.split('-')[1] === 'sky' ? 'rgb(3, 105, 161)' :
              item.text.split('-')[1] === 'purple' ? 'rgb(126, 34, 206)' :
              'rgb(217, 119, 6)', // amber color
              'rgb(203, 213, 225)',
            ],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={index}
              style={{
                width: dotWidth,
                height: 8,
                borderRadius: 4,
                backgroundColor,
                marginHorizontal: 4,
                opacity,
                transform: [
                  {
                    scale: scrollX.interpolate({
                      inputRange,
                      outputRange: [0.8, 1.2, 0.8],
                      extrapolate: 'clamp',
                    }),
                  },
                ],
              }}
            />
          );
        })}
      </View>
    );
  };

  const PromotionalSkeleton = () => (
    <View style={styles.container}>
      <View style={{ flexDirection: 'row' }}>
        {[1].map((item) => (
          <View key={item} style={styles.card}>
            <CardSkeleton height={200} />
          </View>
        ))}
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <PromotionalSkeleton/>
    );
  }

  if (error) {
    return (
      <View className="p-6 items-center">
        <Text className="text-red-500">Failed to load promotions.</Text>
      </View>
    );
  }

  if (!promotions.length) {
    return null;
  }

  return (
    <View>
      <Text className="text-xl font-bold text-gray-800 px-4 mb-3">
        Special Offers & Schemes
      </Text>
      
      <Animated.FlatList
        ref={flatListRef}
        data={extendedPromotions}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        snapToInterval={cardWidth + 16}
        decelerationRate="fast"
        initialScrollIndex={initialScrollIndex}
        getItemLayout={(_, index) => ({
          length: cardWidth + 16,
          offset: (cardWidth + 16) * index,
          index,
        })}
        onScroll={handleScroll}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        contentContainerStyle={{ paddingVertical: 8 }}
      />
      
      {renderDotIndicators()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  card: {
    width: '100%',
    paddingHorizontal: 8,
  }
});

export default PromotionalScroll;
