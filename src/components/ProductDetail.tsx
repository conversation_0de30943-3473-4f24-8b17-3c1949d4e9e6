import {View, Text, TouchableOpacity, ScrollView, Image, Dimensions, Share, FlatList, Modal, Pressable, StyleSheet, ActivityIndicator, Alert} from 'react-native';
import React, {useState, useEffect} from 'react';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useDispatch} from 'react-redux';
import {addToCart} from '../redux/slices/cartSlice';
import Breadcrumb from './common/Breadcrumb';
import {useUser} from '../context/UserContext';
import ProductService from '../services/ProductService';
import { UserRole } from '../data/mockData';
import { useGetProductByIdQuery, useGetVariantsbyProductIdQuery, useGetPricingbyProductAndVariantIdQuery } from '../services/api/apiSlice';
import { useAddToCartMutation } from '../screens/cart/cartApi/apiSlice';
import ImageZoomModal from './common/ImageZoomModal';

type RootStackParamList = {
  ProductDetail: { product?: { id: string } };
  Cart: undefined;
  Checkout: undefined;
  Login: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// API Response Interfaces
interface ProductVariant {
  id: number;
  unitOfMeasurement: string;
  catalogType: string;
  urls: string[];
  color?: string;
}

interface VariantData {
  productId: number;
  productName: string;
  sku: string;
  status: number;
  productVariantDetailsList: ProductVariant[];
}

interface PriceVariantDetail {
  id: number;
  quantity: number;
  unitOfMeasurement: string;
  price: number;
  sellingPrice: number;
  variantId: number;
  variantStatus: number;
}

interface PricingData {
  productId: number;
  productName: string;
  sku: string;
  status: number;
  priceVariantDetailsDTO: PriceVariantDetail[];
}

interface ProductData {
  id: number;
  productCode: string;
  name: string;
  type: number;
  status: number;
  description: string;
  parentId: number;
  brand: number;
  urls: string[];
  updatedBy: number;
  catalogType: string;
}

interface CarouselItem {
  id: string;
  uri: string;
}

const ProductDetail = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<any>();
  const { currentUser } = useUser();
  const [showZoomModal, setShowZoomModal] = useState<boolean>(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedQuantity, setSelectedQuantity] = useState<number>(1);
  const {width: screenWidth} = Dimensions.get('window');

  // API mutation hook for add to cart
  const [addToCartApi, { isLoading: isAddingToCartApi }] = useAddToCartMutation();

  // Get product ID from route params
  const productId = route.params?.product?.id;

  console.log('Product ID:', productId);

  // API Queries
  const {
    data: productData,
    isLoading,
    error
  } = useGetProductByIdQuery(productId?.replace('prod-', ''), {
    skip: !productId
  });

  const {
    data: variantsData,
    isLoading: isVariantsLoading,
    error: variantsError
  } = useGetVariantsbyProductIdQuery(productId?.replace('prod-', ''), {
    skip: !productId
  });

  const {
    data: pricingData,
    isLoading: isPricingLoading,
    error: pricingError
  } = useGetPricingbyProductAndVariantIdQuery(
    {
      productId: productId?.replace('prod-', ''),
      variantId: selectedVariant?.id
    },
    {
      skip: !productId || !selectedVariant?.id
    }
  );

  console.log('Product Data:', productData);
  console.log('Variants Data:', variantsData);
  console.log('Pricing Data:', pricingData);

  // Set default variant when variants data is loaded
  useEffect(() => {
    if (variantsData?.productVariantDetailsList?.length > 0) {
      setSelectedVariant(variantsData.productVariantDetailsList[0]);
    }
  }, [variantsData]);

  // Function to get pricing for selected variant
  const getCurrentVariantPricing = (): PriceVariantDetail | null => {
    if (!selectedVariant || !pricingData?.priceVariantDetailsDTO) {
      return null;
    }

    return pricingData.priceVariantDetailsDTO.find(
      (priceVariant: PriceVariantDetail) => priceVariant.variantId === selectedVariant.id
    ) || pricingData.priceVariantDetailsDTO[0] || null;
  };

  // Function to get the display price (selling price or regular price)
  const getDisplayPrice = (): number => {
    const variantPricing = getCurrentVariantPricing();
    return variantPricing ? ( variantPricing.sellingPrice || variantPricing.price) : '';
  };

  // Function to get original price (for showing discounts)
  const getOriginalPrice = (): number | null => {
    const variantPricing = getCurrentVariantPricing();
    if (variantPricing && variantPricing.sellingPrice < variantPricing.price) {
      return variantPricing.price;
    }
    return null;
  };

  // Function to calculate discount percentage
  const getDiscountPercentage = (): number | null => {
    const variantPricing = getCurrentVariantPricing();
    if (variantPricing && variantPricing.sellingPrice < variantPricing.price) {
      const discount = ((variantPricing.price - variantPricing.sellingPrice) / variantPricing.price) * 100;
      return Math.round(discount);
    }
    return null;
  };

// Convert product images to carousel format with variant support
const getCarouselImages = (): CarouselItem[] => {
    // If no data, return empty array
    if (!productData) return [];
  // If a variant is selected and has images, use variant images
  if (selectedVariant?.urls.length > 0) {
    // Combine images and videos from the selected variant
    const mediaItems = [
      ...selectedVariant.urls.map((uri, index) => ({
        id: `variant-img-${index}`,
        uri: uri,
        type: 'image' as const
      })),
      ...(selectedVariant.videoUrls?.map((videoUrl, index) => ({
        id: `variant-video-${index}`,
        videoUrl: videoUrl,
        type: 'video' as const
      })) || [])
    ];
    return mediaItems;
  }
  
  // Otherwise, use product images
  if (!productData?.urls) return [];

  return productData.urls.map((uri, index) => ({
    id: `product-img-${index}`,
    uri: uri,
    type: 'image' as const
  }));
};

const images = getCarouselImages();

  const openZoomModal = (index: number) => {
    setSelectedImageIndex(index);
    setShowZoomModal(true);
  };

  const closeZoomModal = () => {
    setShowZoomModal(false);
  };


// Reset image index when variant changes
useEffect(() => {
  setCurrentImageIndex(0);
}, [selectedVariant]);

  // Clean HTML description
  const getCleanDescription = (): string => {
    if (!productData?.description) return '';
    return productData.description.replace(/<[^>]*>/g, '');
  };

  const handleAddToCart = async () => {
    if (!productData) return;
    
    // Check if user can see prices
    const canSeePrice = currentUser?.role !== UserRole.PUBLIC;
    if (!canSeePrice) {
      Alert.alert('Login Required', 'Please login to add items to cart');
      return;
    }

    if (!currentUser?.id) {
      Alert.alert('Error', 'User information not available. Please login again.');
      return;
    }

    if (!selectedVariant) {
      Alert.alert('Error', 'Please select a variant before adding to cart');
      return;
    }

    try {
      const displayPrice = getDisplayPrice();
      console.log(`Adding to cart: ${productData.name} - ${ProductService.formatPrice(displayPrice)}`);

      const cartPayload = {
        userId: Number(currentUser.id),
        status: 1,
        cartLine: [
          {
            productId: productData.id,
            variantId: selectedVariant.id,
            status: 1,
            quantity: selectedQuantity,
            price: displayPrice
          }
        ]
      };

      const response = await addToCartApi(cartPayload).unwrap();
      console.log('Add to cart API response:', response);

      const variantName = `${selectedVariant.unitOfMeasurement}${selectedVariant.color ? ` - ${selectedVariant.color}` : ''}`;

      Alert.alert('Success', `${productData.name} added to cart successfully!`);

    } catch (error: any) {
      console.error('Add to cart API error:', error);

      // Fallback to local cart if API fails
      const variantName = `${selectedVariant.unitOfMeasurement}${selectedVariant.color ? ` - ${selectedVariant.color}` : ''}`;

      // dispatch(addToCart({
      //   id: productData.id,
      //   name: productData.name,
      //   price: ProductService.formatPrice(getDisplayPrice()),
      //   quantity: selectedQuantity,
      //   image: images.length > 0 ? images[0].uri : '',
      //   variant: variantName
      // }));

      Alert.alert(
        'Error',
        `${(error?.status + ` status code ` + error.data?.message) || error.message || 'Unknown error'}`
      );
    }
  };

  const handleBuyNow = async () => {
    if (!productData) return;
    
    const canSeePrice = currentUser?.role !== UserRole.PUBLIC;
    if (!canSeePrice) {
      Alert.alert('Login Required', 'Please login to purchase items');
      return;
    }

    if (!currentUser?.id) {
      Alert.alert('Error', 'User information not available. Please login again.');
      return;
    }

    if (!selectedVariant) {
      Alert.alert('Error', 'Please select a variant before proceeding');
      return;
    }

    try {
      const displayPrice = getDisplayPrice();
      console.log(`Buying now: ${productData.name} - ${ProductService.formatPrice(displayPrice)}`);

      const cartPayload = {
        userId: Number(currentUser.id),
        status: 1,
        cartLine: [
          {
            productId: productData.id,
            variantId: selectedVariant.id,
            status: 1,
            quantity: selectedQuantity,
            price: displayPrice
          }
        ]
      };

      const response = await addToCartApi(cartPayload).unwrap();
      console.log('Buy now API response:', response);

      const variantName = `${selectedVariant.unitOfMeasurement}${selectedVariant.color ? ` - ${selectedVariant.color}` : ''}`;

      dispatch(addToCart({
        id: productData.id,
        name: productData.name,
        price: ProductService.formatPrice(displayPrice),
        quantity: selectedQuantity,
        image: images.length > 0 ? images[0].uri : '',
        variant: variantName
      }));

      navigation.navigate('Cart');

    } catch (error: any) {
      console.error('Buy now API error:', error);

      const variantName = `${selectedVariant.unitOfMeasurement}${selectedVariant.color ? ` - ${selectedVariant.color}` : ''}`;

      dispatch(addToCart({
        id: productData.id,
        name: productData.name,
        price: ProductService.formatPrice(getDisplayPrice()),
        quantity: selectedQuantity,
        image: images.length > 0 ? images[0].uri : '',
        variant: variantName
      }));

      navigation.navigate('Cart');
    }
  };

  const handleShare = async (): Promise<void> => {
    if (!productData) return;

    try {
      await Share.share({
        message: `Check out ${productData.name}!\n\n${getCleanDescription()}`
      });
    } catch (error) {
      console.log('Error sharing:', error);
    }
  };

  const onImageScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffsetX / screenWidth);
    setCurrentImageIndex(newIndex);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading product details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="error-outline" size={48} color="#e5e7eb" />
        <Text style={styles.loadingText}>Error loading product</Text>
      </View>
    );
  }

  if (!productData) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="error-outline" size={48} color="#e5e7eb" />
        <Text style={styles.loadingText}>Product not found</Text>
      </View>
    );
  }

  const canSeePrice = currentUser?.role !== UserRole.PUBLIC;

  return (
    <View style={styles.container}>
      <Breadcrumb
        items={[
          { label: 'Home', screen: 'Home' },
          { label: productData.parentName, screen: 'ProductListing', params: { categoryId: productData.parentId } },
          { label: productData.name }
        ]}
      />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity>
            {/* Icon placeholder */}
          </TouchableOpacity>
          <View style={styles.headerActions}>
            <TouchableOpacity
              onPress={handleShare}
              style={styles.iconButton}
              accessibilityLabel="Share product"
            >
              <Icon name="share" size={22} color="#333" />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Scrollable Content */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollContent}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {/* Image Carousel */}
        <View style={styles.carouselContainer}>
          {images.length > 0 ? (
            <>
              <FlatList
                data={images}
                renderItem={({item,index}) => (
                  <Pressable
                    onPress={() => 
                      openZoomModal(index)
                    }
                    style={{width: screenWidth}}
                    accessibilityLabel={`Product image ${item.id}`}
                  >
                    <Image
                      source={{uri: item.uri}}
                      style={styles.carouselImage}
                      onError={(error) => {
                        console.log('Image load error:', error.nativeEvent.error);
                      }}
                    />
                  </Pressable>
                )}
                keyExtractor={(item) => item.id}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onScroll={onImageScroll}
                scrollEventThrottle={16}
                key={selectedVariant?.id || 'product'} // Force re-render when variant changes
              />
              <View style={styles.paginationDots}>
                {images.map((_, index) => (
                  <View
                    key={`dot-${selectedVariant?.id || 'product'}-${index}`}
                    style={[
                      styles.paginationDot,
                      index === currentImageIndex && styles.activePaginationDot
                    ]}
                  />
                ))}
              </View>
            </>
          ) : (
            <View style={styles.noImageContainer}>
              <Icon name="image" size={48} color="#e5e7eb" />
              <Text style={styles.noImageText}>No images available</Text>
            </View>
          )}
        </View>


        {/* Product Info */}
        <View style={styles.productInfoContainer}>
          <Text style={styles.productName}>{productData.name}</Text>
          <Text style={styles.productCode}>Product Code: {productData.productCode}</Text>
          
          {canSeePrice ? (
            <View style={styles.priceContainer}>
              <View style={styles.priceRow}>
                <Text style={styles.productPrice}>
                  {ProductService.formatPrice(getDisplayPrice())}
                </Text>
                {getDiscountPercentage() && (
                  <View style={styles.discountBadge}>
                    <Text style={styles.discountText}>
                      {getDiscountPercentage()}% OFF
                    </Text>
                  </View>
                )}
              </View>
              {getOriginalPrice() && (
                <Text style={styles.originalPrice}>
                  {ProductService.formatPrice(getOriginalPrice()!)}
                </Text>
              )}
              {getCurrentVariantPricing() && (
                <Text style={styles.variantPriceInfo}>
                  Price for {selectedVariant?.quantity + getCurrentVariantPricing()?.unitOfMeasurement} units
                </Text>
              )}
            </View>
          ) : (
            <Text style={styles.loginPrompt}>Login to see price</Text>
          )}

          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>{getCleanDescription()}</Text>
          </View>

          {/* Variant Selection */}
          {variantsData?.productVariantDetailsList && variantsData.productVariantDetailsList.length > 0 && (
            <View style={styles.variantContainer}>
              <Text style={styles.sectionTitle}>Select Variant</Text>
              <View style={styles.variantOptions}>
                {variantsData.productVariantDetailsList.map((variant: ProductVariant) => (
                  <TouchableOpacity
                    key={variant.id}
                    style={[
                      styles.variantButton,
                      selectedVariant?.id === variant.id && styles.selectedVariantButton
                    ]}
                    onPress={() => setSelectedVariant(variant)}
                  >
                    <View style={styles.variantContent}>
                      <Text
                        style={[
                          styles.variantButtonText,
                          selectedVariant?.id === variant.id && styles.selectedVariantButtonText
                        ]}
                      >
                        {/* {variant.unitOfMeasurement} */}
                        {variant.color && ` ${variant.color}`}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Quantity Selector */}
              {selectedVariant && (
                <View style={styles.quantityContainer}>
                  <Text style={styles.quantityLabel}>Quantity:</Text>
                  <View style={styles.quantitySelector}>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => setSelectedQuantity(Math.max(1, selectedQuantity - 1))}
                    >
                      <Icon name="remove" size={20} color="#6366f1" />
                    </TouchableOpacity>
                    <Text style={styles.quantityText}>{selectedQuantity}</Text>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => setSelectedQuantity(selectedQuantity + 1)}
                    >
                      <Icon name="add" size={20} color="#6366f1" />
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          )}
        </View>

        {/* Add extra padding at bottom to ensure content is visible above the fixed buttons */}
        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Footer Buttons - Only shown for logged-in users */}
      {currentUser && currentUser.role !== UserRole.PUBLIC && (
        <View style={styles.footerContainer}>
          <TouchableOpacity
            onPress={handleAddToCart}
            style={[styles.addToCartButton, isAddingToCartApi && styles.disabledButton]}
            disabled={isAddingToCartApi || !selectedVariant}
          >
            {isAddingToCartApi ? (
              <ActivityIndicator size="small" color="#6366f1" style={styles.buttonIcon} />
            ) : (
              <Icon name="shopping-cart" size={18} color="#6366f1" style={styles.buttonIcon} />
            )}
            <Text style={styles.addToCartButtonText}>
              {isAddingToCartApi ? 'Adding...' : 'Add to Cart'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleBuyNow}
            style={[styles.buyNowButton, isAddingToCartApi && styles.disabledButton]}
            disabled={isAddingToCartApi || !selectedVariant}
          >
            {isAddingToCartApi ? (
              <ActivityIndicator size="small" color="white" style={styles.buttonIcon} />
            ) : (
              <Icon name="flash-on" size={18} color="white" style={styles.buttonIcon} />
            )}
            <Text style={styles.buyNowButtonText}>
              {isAddingToCartApi ? 'Processing...' : 'Buy Now'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Login prompt for public users */}
      {(!currentUser || currentUser.role === UserRole.PUBLIC) && (
        <View style={styles.footerContainer}>
          <TouchableOpacity
            onPress={() => navigation.navigate('Login')}
            style={styles.loginButton}
          >
            <Icon name="login" size={18} color="white" style={styles.buttonIcon} />
            <Text style={styles.loginButtonText}>Login to Purchase</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Image Zoom Modal */}
      <ImageZoomModal
        visible={showZoomModal}
        images={images}
        initialIndex={selectedImageIndex}
        onClose={closeZoomModal}
      />
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  iconButton: {
    padding: 8,
    marginLeft: 8,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 16,
  },
  carouselContainer: {
    backgroundColor: 'white',
  },
  carouselImage: {
    width: Dimensions.get('window').width,
    height: 300,
    resizeMode: 'contain',
  },
  paginationDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
    backgroundColor: '#ccc',
  },
  activePaginationDot: {
    backgroundColor: '#6366f1',
    width: 16,
  },
  imageSourceIndicator: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  imageSourceText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  noImageContainer: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  noImageText: {
    marginTop: 8,
    fontSize: 14,
    color: '#9ca3af',
  },
  productInfoContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginTop: 8,
    borderRadius: 8,
  },
  productName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  productCode: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
    fontStyle: 'italic',
  },
  productPrice: {
    fontSize: 20,
    color: '#6366f1',
    fontWeight: 'bold',
    marginTop: 4,
  },
  loginPrompt: {
    fontSize: 16,
    color: '#9ca3af',
    fontStyle: 'italic',
    marginTop: 4,
  },
  descriptionContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    paddingBottom: 16,
    marginTop: 12,
  },
  descriptionText: {
    color: '#666',
    lineHeight: 22,
  },
  variantContainer: {
    marginTop: 20,
  },
  variantOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  variantButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#d1d5db',
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: 'white',
  },
  selectedVariantButton: {
    borderColor: '#6366f1',
    backgroundColor: '#eef2ff',
  },
  variantButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  selectedVariantButtonText: {
    color: '#6366f1',
    fontWeight: '600',
  },
  variantContent: {
    alignItems: 'center',
  },
  quantityContainer: {
    marginTop: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  quantityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: 'white',
  },
  quantityButton: {
    padding: 12,
    borderRadius: 8,
  },
  quantityText: {
    paddingHorizontal: 16,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    minWidth: 40,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  specificationsContainer: {
    marginTop: 24,
  },
  specificationsTable: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 16,
  },
  specificationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  specificationBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  specificationLabel: {
    color: '#666',
    textTransform: 'capitalize',
  },
  specificationValue: {
    color: '#333',
    fontWeight: '500',
  },
  bottomPadding: {
    height: 80,
  },
  footerContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  addToCartButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#eef2ff',
    paddingVertical: 14,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 1,
  },
  addToCartButtonText: {
    color: '#6366f1',
    fontWeight: '600',
    fontSize: 16,
  },
  buyNowButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#6366f1',
    paddingVertical: 14,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 1,
  },
  buyNowButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 6,
  },
  loginButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#6366f1',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loginButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  // Pricing-related styles
  priceContainer: {
    marginTop: 8,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  discountBadge: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  discountText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  originalPrice: {
    fontSize: 16,
    color: '#9ca3af',
    textDecorationLine: 'line-through',
    marginBottom: 4,
  },
  variantPriceInfo: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default ProductDetail;                
