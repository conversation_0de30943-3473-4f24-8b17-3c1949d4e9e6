import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal, Animated } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Slider from '@react-native-community/slider';
import { FilterOptions } from './types';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';

interface FilterModalProps {
  visible: boolean;
  filterOptions: FilterOptions;
  slideAnimation: Animated.Value;
  onClose: () => void;
  onApply: () => void;
  onReset: () => void;
  setFilterOptions: (options: FilterOptions) => void;
  currentCategoryId: number;
}

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  filterOptions,
  slideAnimation,
  onClose,
  onApply,
  onReset,
  setFilterOptions,
  currentCategoryId,
}) => {
  // Get current user
  const { currentUser } = useUser();

  // Local state for price range to prevent jumpy updates
  const [localPriceRange, setLocalPriceRange] = useState(filterOptions.priceRange[1]);
  const [isDragging, setIsDragging] = useState(false);

  // Check if user is public
  const isPublicUser = !currentUser || currentUser.role === UserRole.PUBLIC;

  // Update local state when filterOptions change
  useEffect(() => {
    if (filterOptions?.priceRange && filterOptions.priceRange.length === 2) {
      setLocalPriceRange(filterOptions.priceRange[1]);
    }
  }, [filterOptions]);

  // Handle price range change
  const handlePriceRangeChange = (value: number) => {
    const roundedValue = Math.round(value);
    setLocalPriceRange(roundedValue);
  };

  // Update parent state when sliding is complete
  const handlePriceRangeComplete = (value: number) => {
    const roundedValue = Math.round(value);
    setFilterOptions({
      ...filterOptions,
      priceRange: [0, roundedValue]
    });

    // Log for debugging
    console.log(`Price range set to: ₹0 - ₹${roundedValue}`);
  };

  // Format price to Indian Rupees
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/50 justify-end">
        <Animated.View
          style={[
            {
              backgroundColor: 'white',
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              maxHeight: '80%',
            },
            {
              transform: [
                {
                  translateY: slideAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [600, 0],
                  }),
                },
              ],
            },
          ]}
        >
          {/* Modal Header */}
          <View className="flex-row justify-between items-center p-4 border-b border-gray-200">
            <Text className="text-lg font-bold text-gray-800">Filter Products</Text>
            <TouchableOpacity onPress={onClose}>
              <Icon name="close" size={24} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <ScrollView className="p-4">
            {/* Price Range Section - Hidden for public users */}
            {!isPublicUser && (
              <View className="mb-6">
                <Text className="text-base font-bold text-gray-800 mb-2">Price Range</Text>
                <View>
                  <View className="flex-row flex-wrap gap-2 mb-3">
                    {[1000, 2000, 5000, 10000].map((price) => (
                      <TouchableOpacity
                        key={price}
                        onPress={() => {
                          // Update local state
                          setLocalPriceRange(price);

                          // Update parent component's filter options
                          setFilterOptions({
                            ...filterOptions,
                            priceRange: [0, price] as [number, number]
                          });

                          console.log(`Price range preset selected: ₹0 - ₹${price}`);
                        }}
                        className={`py-2 px-4 rounded-lg border ${
                          localPriceRange === price
                            ? 'bg-indigo-100 border-indigo-300'
                            : 'bg-white border-gray-300'
                        }`}
                      >
                        <Text className={`${
                          localPriceRange === price
                            ? 'text-indigo-700'
                            : 'text-gray-700'
                        }`}>
                          Under {formatPrice(price)}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                  <View className="flex-row items-center gap-2">
                    <View className="flex-1">
                      <Text className="text-sm text-gray-600 mb-1">Custom Range</Text>
                      <TouchableOpacity
                        className={`py-2 px-4 rounded-lg border ${
                          !([1000, 2000, 5000, 10000].includes(localPriceRange))
                            ? 'bg-indigo-100 border-indigo-300'
                            : 'bg-white border-gray-300'
                        }`}
                        onPress={() => {
                          // Ensure the value is within reasonable bounds
                          const customValue = Math.min(Math.max(500, localPriceRange), 50000);

                          // Update local state
                          setLocalPriceRange(customValue);

                          // Update parent component's filter options
                          setFilterOptions({
                            ...filterOptions,
                            priceRange: [0, customValue] as [number, number]
                          });

                          console.log(`Custom price range set: ₹0 - ₹${customValue}`);
                        }}
                      >
                        <Text className={`${
                          !([1000, 2000, 5000, 10000].includes(localPriceRange))
                            ? 'text-indigo-700'
                            : 'text-gray-700'
                        }`}>
                          Up to {formatPrice(localPriceRange)}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>
            )}

            {/* Sort By Section */}
            <View className="mb-6">
              <Text className="text-base font-bold text-gray-800 mb-2">Sort By</Text>
              <View className="flex-row flex-wrap gap-2">
                {[
                  { id: 'popularity', label: 'Popularity' },
                  // Only show price sorting options for non-public users
                  ...(!isPublicUser ? [
                    { id: 'price_low', label: 'Price: Low to High' },
                    { id: 'price_high', label: 'Price: High to Low' },
                  ] : []),
                  { id: 'newest', label: 'Newest First' },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    onPress={() => {
                      setFilterOptions({
                        ...filterOptions,
                        sortBy: option.id
                      });
                      console.log(`Sort option selected: ${option.label}`);
                    }}
                    className={`px-3 py-1 rounded-full border ${
                      filterOptions.sortBy === option.id
                        ? 'bg-indigo-100 border-indigo-300'
                        : 'bg-white border-gray-300'
                    }`}
                  >
                    <Text
                      className={`text-sm ${
                        filterOptions.sortBy === option.id
                          ? 'text-indigo-700'
                          : 'text-gray-700'
                      }`}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Availability Section */}
            <View className="mb-6">
              <Text className="text-base font-bold text-gray-800 mb-2">Availability</Text>
              <TouchableOpacity
                onPress={() => {
                  const newValue = !filterOptions.inStock;
                  setFilterOptions({
                    ...filterOptions,
                    inStock: newValue
                  });
                  console.log(`In-stock filter ${newValue ? 'enabled' : 'disabled'}`);
                }}
                className="flex-row items-center"
              >
                <View className={`w-5 h-5 rounded border mr-2 ${
                  filterOptions.inStock
                    ? 'bg-indigo-500 border-indigo-500'
                    : 'bg-white border-gray-300'
                }`}>
                  {filterOptions.inStock && (
                    <Icon name="check" size={16} color="white" />
                  )}
                </View>
                <Text className="text-gray-700">Show only in-stock items</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>

          {/* Footer Buttons */}
          <View className="p-4 border-t border-gray-200 flex-row">
            <TouchableOpacity
              onPress={onReset}
              className="flex-1 mr-2 py-3 border border-gray-300 rounded-lg items-center"
            >
              <Text className="text-gray-700 font-medium">Reset</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                console.log('Apply button pressed with filters:', JSON.stringify(filterOptions));
                onApply();
              }}
              className="flex-1 ml-2 py-3 bg-indigo-600 rounded-lg items-center"
            >
              <Text className="text-white font-medium">Apply Filters</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default FilterModal;