import React from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface SortModalProps {
  visible: boolean;
  currentSort: string;
  onClose: () => void;
  onSelect: (option: string) => void;
}

const SortModal: React.FC<SortModalProps> = ({
  visible,
  currentSort,
  onClose,
  onSelect,
}) => {
  const sortOptions = [
    { id: 'popularity', label: 'Popularity' },
    { id: 'price_low', label: 'Price: Low to High' },
    { id: 'price_high', label: 'Price: High to Low' },
    { id: 'newest', label: 'Newest First' },
  ];

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)' }}
        activeOpacity={1}
        onPress={onClose}
      >
        <View className="m-4 mt-20 bg-white rounded-lg overflow-hidden">
          <View className="p-4 border-b border-gray-200">
            <Text className="text-lg font-bold text-gray-800">Sort By</Text>
          </View>
          
          {sortOptions.map((option) => (
            <TouchableOpacity 
              key={option.id}
              onPress={() => {
                onSelect(option.id);
                onClose();
              }}
              className="p-4 border-b border-gray-100 flex-row justify-between items-center"
            >
              <Text className="text-gray-700">{option.label}</Text>
              {currentSort === option.id && (
                <Icon name="check" size={20} color="#6366f1" />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

export default SortModal;