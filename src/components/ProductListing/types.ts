import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';

export interface Product {
  id: number;
  name: string;
  description: string;
  price: string;
  unitPrice: string;
  quantity: number;
  image: string;
  category?: string;
  categoryId?: number;
  productCode?: string;
}

export interface FilterOptions {
  priceRange: [number, number];
  categories: string[];
  sortBy: string;
  inStock: boolean;
}

export type RootStackParamList = {
  ProductDetail: any;
  ProductListing: {
    category?: string;
    categoryId?: number;
  };
};

export type ProductListingScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList>;
  route: RouteProp<RootStackParamList, 'ProductListing'>;
};