import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Portal, Modal, Provider, ActivityIndicator } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useGetAllSchemesQuery } from '../screens/management/api/scheme';

const Schemes = () => {
  const navigation = useNavigation();
  
  // API hook for fetching schemes
  const { data, isLoading, error } = useGetAllSchemesQuery();

  const [selectedScheme, setSelectedScheme] = useState(null);
  const [visible, setVisible] = useState(false);

  const showModal = (scheme) => {
    setSelectedScheme(scheme);
    setVisible(true);
  };

  const hideModal = () => {
    setVisible(false);
    setSelectedScheme(null);
  };

  console.log('Schemes Data', data);

  // Transform API data to match component expectations
  const getDisplaySchemes = () => {
    if (data?.data?.data && Array.isArray(data.data.data)) {
      return data.data.data.map((scheme, index) => ({
        id: scheme.id || index + 1,
        title: scheme.name,
        description: scheme.description,
        validTill: formatDate(scheme.endDate),
        tag: scheme.offer === 'TRIP' ? 'Trip Offer' : 'Gift Offer',
        imageUrl: scheme.imageUrl || `https://via.placeholder.com/400x300/6366f1/ffffff?text=${encodeURIComponent(scheme.name || 'Scheme')}`,
        purchaseAmount: scheme.purchaseAmount,
        startDate: scheme.startDate,
        endDate: scheme.endDate,
        status: scheme.status,
        offer: scheme.offer
      }));
    }
    return [];
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-IN', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  };

  const schemes = getDisplaySchemes();

  if (isLoading) {
    return (
      <Provider>
        <View className="flex-1 justify-center items-center bg-white">
          <ActivityIndicator size="large" color="#6366f1" />
          <Text className="text-gray-600 mt-4">Loading schemes...</Text>
        </View>
      </Provider>
    );
  }

  if (error) {
    console.error('Schemes API Error:', error);
    return (
      <Provider>
        <View className="flex-1 justify-center items-center bg-white">
          <Icon name="error-outline" size={64} color="#ef4444" />
          <Text className="text-red-500 text-lg mt-4">Error loading schemes</Text>
          <Text className="text-gray-500 text-sm mt-2 text-center px-8">
            Please check your connection and try again
          </Text>
        </View>
      </Provider>
    );
  }

  return (
    <Provider>
      <ScrollView className="flex-1 bg-gray-100 px-3 pt-4">
        {schemes.length > 0 ? (
          schemes.map((scheme) => (
            <TouchableOpacity key={scheme.id} onPress={() => showModal(scheme)}>
              <View className="bg-white mb-4 rounded-2xl shadow-md overflow-hidden">
                <Image
                  source={{ uri: scheme.imageUrl }}
                  className="h-48 w-full"
                  resizeMode="cover"
                />
                <View className="p-4">
                  <View className="flex-row justify-between items-start mb-2">
                    <View className="flex-1">
                      <Text className="text-lg font-bold text-gray-800 mb-1">
                        {scheme.title}
                      </Text>
                      <View className="bg-blue-50 self-start px-3 py-1 rounded-full mb-2">
                        <Text className="text-blue-600 text-xs font-semibold">
                          {scheme.tag}
                        </Text>
                      </View>
                    </View>
                    <Icon name="info-outline" size={22} color="#6B7280" />
                  </View>
                  
                  <Text className="text-gray-600 text-sm mb-3 leading-5">
                    {scheme.description}
                  </Text>
                  
                  {scheme.purchaseAmount && (
                    <View className="bg-green-50 px-3 py-2 rounded-lg mb-3">
                      <Text className="text-green-700 text-sm font-semibold">
                        Min. Purchase: ₹{scheme.purchaseAmount.toLocaleString()}
                      </Text>
                    </View>
                  )}
                  
                  <View className="flex-row justify-between items-center">
                    <Text className="text-gray-500 text-sm">
                      Valid till {scheme.validTill}
                    </Text>
                    <View className="bg-orange-50 px-3 py-1 rounded-full">
                      <Text className="text-orange-600 text-xs font-semibold">
                        {scheme.offer === 'TRIP' ? '🎯 Trip Reward' : '🎁 Gift Reward'}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View className="flex-1 justify-center items-center py-20">
            <Icon name="local-offer" size={64} color="#d1d5db" />
            <Text className="text-gray-500 text-lg mt-4">No schemes available</Text>
            <Text className="text-gray-400 text-sm mt-2 text-center px-8">
              Check back later for exciting schemes and offers
            </Text>
          </View>
        )}
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={hideModal}
          contentContainerStyle={{
            backgroundColor: '#fff',
            padding: 20,
            marginHorizontal: 20,
            borderRadius: 16,
            maxHeight: '80%',
          }}
        >
          {selectedScheme && (
            <ScrollView showsVerticalScrollIndicator={false}>
              <Text className="text-xl font-bold text-gray-900 mb-3">
                {selectedScheme.title}
              </Text>
              
              <Image
                source={{ uri: selectedScheme.imageUrl }}
                style={{
                  width: '100%',
                  height: 160,
                  borderRadius: 12,
                  marginBottom: 16,
                }}
                resizeMode="cover"
              />
              
              <View className="bg-blue-50 p-4 rounded-lg mb-4">
                <Text className="text-blue-800 font-semibold text-base mb-2">
                  Scheme Details
                </Text>
                <Text className="text-blue-700 text-sm leading-5">
                  {selectedScheme.description}
                </Text>
              </View>

              <View className="space-y-3 mb-6">
                <View className="flex-row items-center">
                  <Icon name="local-offer" size={20} color="#6366f1" />
                  <Text className="text-gray-700 ml-3">
                    <Text className="font-semibold">Offer Type:</Text> {selectedScheme.offer === 'TRIP' ? 'Trip Reward' : 'Gift Reward'}
                  </Text>
                </View>
                
                {selectedScheme.purchaseAmount && (
                  <View className="flex-row items-center">
                    <Icon name="account-balance-wallet" size={20} color="#059669" />
                    <Text className="text-gray-700 ml-3">
                      <Text className="font-semibold">Min. Purchase:</Text> ₹{selectedScheme.purchaseAmount.toLocaleString()}
                    </Text>
                  </View>
                )}
                
                <View className="flex-row items-center">
                  <Icon name="event" size={20} color="#dc2626" />
                  <Text className="text-gray-700 ml-3">
                    <Text className="font-semibold">Valid From:</Text> {formatDate(selectedScheme.startDate)}
                  </Text>
                </View>
                
                <View className="flex-row items-center">
                  <Icon name="event-busy" size={20} color="#dc2626" />
                  <Text className="text-gray-700 ml-3">
                    <Text className="font-semibold">Valid Till:</Text> {selectedScheme.validTill}
                  </Text>
                </View>
                
                <View className="flex-row items-center">
                  <Icon name="verified" size={20} color="#16a34a" />
                  <Text className="text-gray-700 ml-3">
                    <Text className="font-semibold">Status:</Text> {selectedScheme.status === 1 ? 'Active' : 'Inactive'}
                  </Text>
                </View>
              </View>

              <TouchableOpacity
                className="bg-blue-600 py-4 rounded-lg mb-2"
                onPress={hideModal}
              >
                <Text className="text-center text-white font-semibold text-base">
                  Close
                </Text>
              </TouchableOpacity>
            </ScrollView>
          )}
        </Modal>
      </Portal>
    </Provider>
  );
};

export default Schemes;
