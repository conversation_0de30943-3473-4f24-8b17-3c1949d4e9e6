import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface EmptyStateProps {
  icon: string;
  message: string;
  actionLabel?: string;
  onAction?: () => void;
  iconColor?: string;
}

/**
 * Reusable empty state component for lists and screens
 */
const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  message,
  actionLabel,
  onAction,
  iconColor = '#e5e7eb',
}) => {
  return (
    <View style={styles.container}>
      <Icon name={icon} size={64} color={iconColor} />
      <Text variant="bodyLarge" style={styles.message}>
        {message}
      </Text>
      {actionLabel && onAction && (
        <Button
          mode="contained"
          onPress={onAction}
          style={styles.actionButton}
        >
          {actionLabel}
        </Button>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    marginTop: 64,
  },
  message: {
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
    color: '#6b7280',
  },
  actionButton: {
    marginTop: 16,
  },
});

export default EmptyState;
