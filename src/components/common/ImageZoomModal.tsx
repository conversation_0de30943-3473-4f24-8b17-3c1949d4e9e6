import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Modal,
  Dimensions,
  StyleSheet,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {
  GestureHandlerRootView,
  PinchGestureHandler,
  PanGestureHandler,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import Video from 'react-native-video';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ImageItem {
  id: string;
  uri: string;
  videoUrl?: string;
  type: 'image' | 'video';
}

interface ImageZoomModalProps {
  visible: boolean;
  images: ImageItem[];
  initialIndex: number;
  onClose: () => void;
}

const ImageZoomModal: React.FC<ImageZoomModalProps> = ({
  visible,
  images,
  initialIndex,
  onClose,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      resetZoom()
    }
  }, [currentIndex, visible]);

  const resetZoom = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
  };

  const navigateImage = (direction: 'left' | 'right') => {
    const maxIndex = images.length - 1;
    let newIndex = currentIndex;
    if (direction === 'left' && currentIndex > 0) newIndex = currentIndex - 1;
    else if (direction === 'right' && currentIndex < maxIndex) newIndex = currentIndex + 1;
    if (newIndex !== currentIndex) setCurrentIndex(newIndex);
  };

  const pinchHandler = useAnimatedGestureHandler({
    onStart: (_, context: any) => { context.startScale = scale.value; },
    onActive: (event, context) => { scale.value = Math.max(1, Math.min(context.startScale * event.scale, 4)); },
    onEnd: () => { if (scale.value < 1.2) resetZoom(); },
  });

  const panHandler = useAnimatedGestureHandler({
    onStart: (_, context: any) => { context.startX = translateX.value; context.startY = translateY.value; },
    onActive: (event, context) => {
      if (scale.value > 1) {
        const maxTranslateX = (screenWidth * (scale.value - 1)) / 2;
        const maxTranslateY = (screenHeight * (scale.value - 1)) / 2;
        translateX.value = Math.max(-maxTranslateX, Math.min(maxTranslateX, context.startX + event.translationX));
        translateY.value = Math.max(-maxTranslateY, Math.min(maxTranslateY, context.startY + event.translationY));
      } else {
        translateX.value = context.startX + event.translationX;
      }
    },
    onEnd: (event) => {
      if (scale.value <= 1 && Math.abs(event.translationX) > 100) {
        runOnJS(navigateImage)(event.translationX > 0 ? 'left' : 'right');
      }
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  if (!visible || images.length === 0) return null;

  const currentImage = images[currentIndex];

  return (
    <Modal
      visible={visible}
      transparent={false}
      onRequestClose={onClose}
      statusBarTranslucent={false}
      animationType="fade"
    >
      <StatusBar backgroundColor="white" barStyle="dark-content" />
      <GestureHandlerRootView style={styles.container}>
        <View style={styles.fullScreenContainer}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Icon name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.counterText}>
              {currentIndex + 1} of {images.length}
            </Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.imageContainer}>
            <PanGestureHandler onGestureEvent={panHandler}>
              <Animated.View style={styles.imageWrapper}>
                <PinchGestureHandler onGestureEvent={pinchHandler}>
                  <Animated.View style={animatedStyle}>
                    <TouchableOpacity 
                      activeOpacity={1}
                      onPress={() => scale.value > 1 && resetZoom()}
                    >
                      {currentImage.type === 'video' ? (
                        <Video
                          source={{ uri: currentImage.videoUrl }}
                          style={styles.fullScreenImage}
                          resizeMode="contain"
                          controls={true}
                          paused={false}
                          ignoreSilentSwitch="ignore"
                        />
                      ) : (
                        <Image
                          source={{ uri: currentImage.uri }}
                          style={styles.fullScreenImage}
                          resizeMode="contain"
                        />
                      )}
                    </TouchableOpacity>
                  </Animated.View>
                </PinchGestureHandler>
              </Animated.View>
            </PanGestureHandler>
          </View>
        </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  fullScreenContainer: { flex: 1, backgroundColor: 'white' },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  closeButton: { padding: 8, borderRadius: 20, backgroundColor: '#f8f8f8' },
  counterText: { fontSize: 16, color: '#333', fontWeight: '500' },
  placeholder: { width: 40 },
  imageContainer: {
    flex: 1,
    backgroundColor: '#fafafa',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageWrapper: {
    width: screenWidth,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    width: screenWidth - 20,
    height: screenHeight - 100,
    maxWidth: screenWidth - 20,
    maxHeight: screenHeight - 100,
  },
});

export default ImageZoomModal;