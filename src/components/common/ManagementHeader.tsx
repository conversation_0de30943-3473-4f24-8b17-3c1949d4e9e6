import React from 'react';
import { Appbar, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { StyleSheet, View } from 'react-native';

interface ManagementHeaderProps {
  title: string;
  showBack?: boolean;
  rightActions?: React.ReactNode;
  subtitle?: string;
}

const ManagementHeader: React.FC<ManagementHeaderProps> = ({
  title,
  showBack = true,
  rightActions,
  subtitle
}) => {
  const navigation = useNavigation();
  const theme = useTheme();

  return (
    <View>
      <Appbar.Header
        mode="small"
        style={[styles.header, { backgroundColor: theme.colors.primary }]}
      >
        {showBack && (
          <Appbar.BackAction
            onPress={() => navigation.goBack()}
            color="white"
          />
        )}
        <Appbar.Content
          title={title}
          titleStyle={styles.title}
          subtitle={subtitle}
          subtitleStyle={styles.subtitle}
          color="white"
        />
        {rightActions}
      </Appbar.Header>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
  }
});

export default ManagementHeader;
