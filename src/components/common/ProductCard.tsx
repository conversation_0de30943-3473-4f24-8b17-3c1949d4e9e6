import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ProductCardProps {
  productId?: string;
  productName: string;
  description: string;
  imageUrl: string;
  category?: string;
  onShare?: () => void;
  onPress?: () => void;
  style?: ViewStyle;
  showBadge?: boolean;
  badgeText?: string;
}

type RootStackParamList = {
  ProductDetail: { product: any };
  // ... other routes
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'ProductDetail'>;

const ProductCard: React.FC<ProductCardProps> = ({
  productId,
  productName,
  description,
  imageUrl,
  category,
  onPress,
  style,
  showBadge = true,
  badgeText = 'New',

}) => {
  const navigation = useNavigation<NavigationProp>();

  const handleProductPress = () => {
    if (onPress) {
      onPress();
    } else {
      // Navigate to product detail
      navigation.navigate('ProductDetail', { product: { id: productId } });
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container,style]}
      onPress={handleProductPress}
      activeOpacity={0.9}
    >
      {/* Badge for new or featured products */}
      <View style={styles.badge}>
        <Text style={styles.badgeText}>{badgeText}</Text>
      </View>

      {/* Product Image */}
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: imageUrl }}
          style={styles.image}
          resizeMode="contain"
        />
      </View>

      {/* Product Info */}
      <View style={styles.infoContainer}>
        <Text style={styles.category}>{category || 'Electronics'}</Text>
        <Text style={styles.name} numberOfLines={1}>{productName}</Text>
        <Text style={styles.description} numberOfLines={2}>{description}</Text>

        <TouchableOpacity
          style={styles.button}
          onPress={handleProductPress}
        >
          <Text style={styles.buttonText}>View Details</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 180,
    height: 300,
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 12,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignSelf:'stretch',
  },
  badge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#FFD700',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  imageContainer: {
    height: 120,
    width:'100%',
    backgroundColor: '#f9fafb',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    padding: 12,
    flex: 1,
    justifyContent: 'space-between',
  },
  category: {
    fontSize: 10,
    color: '#6366f1',
    fontWeight: '600',
    textTransform: 'uppercase',
    marginBottom: 4,
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  description: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  specRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  specItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  specText: {
    fontSize: 10,
    color: '#4b5563',
    marginLeft: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  button: {
    backgroundColor: '#6366f1',
    paddingVertical: 6,
    borderRadius: 6,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default ProductCard;
