import React from 'react';
import { Animated, View, StyleSheet, Dimensions, ViewStyle } from 'react-native';

interface ShimmerProps {
  style?: ViewStyle;
  width?: number | string;
  height?: number | string;
  duration?: number;
  backgroundColor?: string;
  highlightColor?: string;
}

const Shimmer: React.FC<ShimmerProps> = ({
  style,
  width = '100%',
  height = '100%',
  duration = 1500,
  // Updated colors for better visual appearance
  backgroundColor = '#f3f4f6', // Light gray base
  highlightColor = '#ffffff'   // White shimmer
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;
  const { width: screenWidth } = Dimensions.get('window');

  React.useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: duration,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    );
    shimmerAnimation.start();
    return () => shimmerAnimation.stop();
  }, [duration]);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-screenWidth, screenWidth]
  });

  return (
    <View 
      style={[
        styles.container,
      ]}
    >
      <Animated.View
        style={[
          styles.shimmer,
          {
            backgroundColor: highlightColor,
            // Updated opacity for subtler effect
            opacity: 0.3,
            transform: [{ translateX }]
          }
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: '#f3f4f6', // Updated base color
    borderRadius: 8, // Increased border radius
  },
  shimmer: {
    width: '100%',
    height: '100%',
    opacity: 0.3,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#ffffff', // Updated highlight color
  }
});

export default Shimmer;