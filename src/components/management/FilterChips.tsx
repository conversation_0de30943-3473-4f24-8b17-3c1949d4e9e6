import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Chip, useTheme } from 'react-native-paper';

interface FilterOption {
  id: string;
  label: string;
}

interface FilterChipsProps {
  options: FilterOption[];
  selectedId: string | null;
  onSelect: (id: string) => void;
  scrollable?: boolean;
}

const FilterChips: React.FC<FilterChipsProps> = ({
  options,
  selectedId,
  onSelect,
  scrollable = true,
}) => {
  const theme = useTheme();

  const renderChips = () => (
    options.map(option => (
      <Chip
        key={option.id}
        selected={selectedId === option.id}
        onPress={() => onSelect(option.id)}
        style={[
          styles.chip,
          selectedId === option.id && { backgroundColor: theme.colors.primaryContainer }
        ]}
        textStyle={[
          styles.chipText,
          selectedId === option.id && { color: theme.colors.primary, fontWeight: '500' }
        ]}
        showSelectedCheck={false}
        mode="flat"
      >
        {option.label}
      </Chip>
    ))
  );

  if (scrollable) {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {renderChips()}
      </ScrollView>
    );
  }

  return (
    <View style={styles.container}>
      {renderChips()}
    </View>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
  },
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
  },
  chip: {
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#f3f4f6',
  },
  chipText: {
    color: '#6b7280',
  },
});

export default FilterChips;
