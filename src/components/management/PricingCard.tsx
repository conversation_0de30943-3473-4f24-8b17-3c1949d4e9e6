import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text, TextInput, Switch, useTheme, Button } from 'react-native-paper';

interface PricingCardProps {
  productId: string;
  productName: string;
  basePrice: number;
  margin: number;
  finalPrice: number;
  category?: string;
  onMarginChange: (productId: string, margin: number) => void;
  onCustomPriceToggle: (productId: string, enabled: boolean) => void;
  onCustomPriceChange: (productId: string, price: number) => void;
  canEdit: boolean;
}

const PricingCard: React.FC<PricingCardProps> = ({
  productId,
  productName,
  basePrice,
  margin,
  finalPrice,
  category,
  onMarginChange,
  onCustomPriceToggle,
  onCustomPriceChange,
  canEdit,
}) => {
  const theme = useTheme();
  const [customPriceEnabled, setCustomPriceEnabled] = useState(false);
  const [customPrice, setCustomPrice] = useState(finalPrice.toString());
  const [marginValue, setMarginValue] = useState(margin.toString());

  // Handle margin change
  const handleMarginChange = (value: string) => {
    setMarginValue(value);
    const numValue = parseFloat(value) || 0;
    onMarginChange(productId, numValue);
  };

  // Handle custom price toggle
  const handleCustomPriceToggle = (value: boolean) => {
    setCustomPriceEnabled(value);
    onCustomPriceToggle(productId, value);
  };

  // Handle custom price change
  const handleCustomPriceChange = (value: string) => {
    setCustomPrice(value);
    const numValue = parseFloat(value) || 0;
    onCustomPriceChange(productId, numValue);
  };

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <Text variant="titleMedium" style={styles.productName}>{productName}</Text>
          {category && (
            <View style={[styles.categoryBadge, { backgroundColor: theme.colors.primaryContainer }]}>
              <Text style={[styles.categoryText, { color: theme.colors.primary }]}>{category}</Text>
            </View>
          )}
        </View>

        <View style={styles.priceGrid}>
          <View style={styles.priceColumn}>
            <Text style={styles.priceLabel}>Base Price</Text>
            <View style={styles.priceValueContainer}>
              <Text style={styles.currencySymbol}>₹</Text>
              <Text style={styles.priceValue}>{basePrice.toFixed(2)}</Text>
            </View>
          </View>

          <View style={styles.priceColumn}>
            <Text style={styles.priceLabel}>Margin (%)</Text>
            {canEdit ? (
              <TextInput
                mode="outlined"
                value={marginValue}
                onChangeText={handleMarginChange}
                keyboardType="numeric"
                style={styles.marginInput}
                outlineStyle={styles.inputOutline}
                dense
                disabled={!canEdit || customPriceEnabled}
              />
            ) : (
              <View style={styles.priceValueContainer}>
                <Text style={styles.priceValue}>{margin}%</Text>
              </View>
            )}
          </View>

          <View style={styles.priceColumn}>
            <Text style={styles.priceLabel}>Final Price</Text>
            <View style={styles.priceValueContainer}>
              <Text style={styles.currencySymbol}>₹</Text>
              <Text style={styles.priceValue}>{finalPrice.toFixed(2)}</Text>
            </View>
          </View>
        </View>

        {canEdit && (
          <View style={styles.customPriceContainer}>
            <View style={styles.customPriceHeader}>
              <Text style={styles.customPriceLabel}>Custom Price</Text>
              <Switch
                value={customPriceEnabled}
                onValueChange={handleCustomPriceToggle}
                color={theme.colors.primary}
              />
            </View>
            {customPriceEnabled && (
              <View style={styles.customPriceInputContainer}>
                <TextInput
                  mode="outlined"
                  label="Custom Price"
                  value={customPrice}
                  onChangeText={handleCustomPriceChange}
                  keyboardType="numeric"
                  style={styles.customPriceInput}
                  outlineStyle={styles.inputOutline}
                  left={<TextInput.Affix text="₹" />}
                  dense
                />
              </View>
            )}
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  productName: {
    fontWeight: 'bold',
    flex: 1,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  priceGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  priceColumn: {
    flex: 1,
    marginHorizontal: 4,
  },
  priceLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  priceValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
  },
  currencySymbol: {
    fontSize: 16,
    color: '#4b5563',
    marginRight: 2,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
  },
  marginInput: {
    backgroundColor: '#f9fafb',
    height: 40,
  },
  inputOutline: {
    borderRadius: 8,
  },
  customPriceContainer: {
    marginTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 12,
  },
  customPriceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  customPriceLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
  },
  customPriceInputContainer: {
    marginTop: 8,
  },
  customPriceInput: {
    backgroundColor: '#f9fafb',
  },
});

export default PricingCard;
