import React, { useState } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  FlatList,
} from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  Searchbar,
  Button,
  Checkbox,
  useTheme,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import EmptyState from '../common/EmptyState';

interface ProductItem {
  id: string;
  name: string;
  productCode?: string;
  category: string;
  basePrice?: number;
}

interface ProductSelectionModalProps {
  visible: boolean;
  onDismiss: () => void;
  products: ProductItem[];
  selectedProducts: ProductItem[];
  onSelectionChange: (products: ProductItem[]) => void;
  title: string;
  isLoading?: boolean;
  multiSelect?: boolean;
}

const ProductSelectionModal: React.FC<ProductSelectionModalProps> = ({
  visible,
  onDismiss,
  products,
  selectedProducts,
  onSelectionChange,
  title,
  isLoading = false,
  multiSelect = true,
}) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Get unique categories
  const categories = Array.from(new Set(products.map(product => product.category))).filter(Boolean);

  // Filter products based on search query and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = !searchQuery ||
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.productCode && product.productCode.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = !selectedCategory || product.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Check if product is selected
  const isProductSelected = (product: ProductItem): boolean => {
    return selectedProducts.some(selected => selected.id === product.id);
  };

  // Toggle product selection
  const toggleProductSelection = (product: ProductItem) => {
    if (multiSelect) {
      if (isProductSelected(product)) {
        // Remove product from selection
        onSelectionChange(selectedProducts.filter(selected => selected.id !== product.id));
      } else {
        // Add product to selection
        onSelectionChange([...selectedProducts, product]);
      }
    } else {
      // Single select mode
      onSelectionChange(isProductSelected(product) ? [] : [product]);
    }
  };

  // Select all products
  const selectAllProducts = () => {
    onSelectionChange(filteredProducts);
  };

  // Clear all selections
  const clearAllSelections = () => {
    onSelectionChange([]);
  };

  // Apply and close modal
  const handleApply = () => {
    onDismiss();
  };

  // Handle category filter
  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? null : category);
  };

  // Render product item
  const renderProductItem = ({ item }: { item: ProductItem }) => (
    <Surface style={styles.productItem} elevation={1}>
      <View style={styles.productInfo}>
        <View style={styles.productDetails}>
          <Text variant="titleSmall" style={styles.productName}>
            {item.name}
          </Text>
          {item.productCode && (
            <Text variant="bodySmall" style={styles.productCode}>
              Code: {item.productCode}
            </Text>
          )}
          <Text variant="bodySmall" style={styles.productCategory}>
            Category: {item.category}
          </Text>
          {item.basePrice && (
            <Text variant="bodySmall" style={styles.productPrice}>
              Price: ₹{item.basePrice}
            </Text>
          )}
        </View>
        <Checkbox
          status={isProductSelected(item) ? 'checked' : 'unchecked'}
          onPress={() => toggleProductSelection(item)}
          theme={{ colors: { primary: theme.colors.primary } }}
        />
      </View>
    </Surface>
  );

  return (
    <Modal
      visible={visible}
      onRequestClose={onDismiss}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <Surface style={styles.container} elevation={4}>
        
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineSmall" style={styles.title}>
            {title}
          </Text>
          <IconButton
            icon="close"
            size={24}
            onPress={onDismiss}
          />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search products..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            iconColor={theme.colors.primary}
          />
        </View>

        {/* Category Filters */}
        {categories.length > 0 && (
          <View style={styles.categoryContainer}>
            <Text variant="bodyMedium" style={styles.categoryLabel}>Categories:</Text>
            <View style={styles.categoryChips}>
              {categories.map((category) => (
                <Chip
                  key={category}
                  mode={selectedCategory === category ? 'flat' : 'outlined'}
                  selected={selectedCategory === category}
                  onPress={() => handleCategoryFilter(category)}
                  style={styles.categoryChip}
                >
                  {category}
                </Chip>
              ))}
            </View>
          </View>
        )}

        {/* Bulk Actions */}
        {multiSelect && (
          <View style={styles.bulkActions}>
            <Text variant="bodyMedium" style={styles.selectedCount}>
              {selectedProducts.length} selected
            </Text>
            <View style={styles.bulkButtons}>
              <Button
                mode="text"
                onPress={selectAllProducts}
                disabled={filteredProducts.length === 0}
              >
                Select All
              </Button>
              <Button
                mode="text"
                onPress={clearAllSelections}
                disabled={selectedProducts.length === 0}
              >
                Clear All
              </Button>
            </View>
          </View>
        )}

        {/* Loading State */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading products...</Text>
          </View>
        )}

        {/* Product List */}
        {!isLoading && (
          <>
            {filteredProducts.length === 0 ? (
              <EmptyState
                icon="package-variant"
                message={searchQuery || selectedCategory ? 'No products match your filters' : 'No products available'}
              />
            ) : (
              <FlatList
                data={filteredProducts}
                renderItem={renderProductItem}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.productList}
                showsVerticalScrollIndicator={false}
              />
            )}
          </>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Apply ({selectedProducts.length})
          </Button>
        </View>

      </Surface>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontWeight: 'bold',
    color: '#374151',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: 'white',
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  categoryContainer: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  categoryLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  categoryChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  bulkActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  selectedCount: {
    color: '#6b7280',
    fontWeight: '500',
  },
  bulkButtons: {
    flexDirection: 'row',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    color: '#6b7280',
  },
  productList: {
    padding: 16,
    paddingBottom: 80,
  },
  productItem: {
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  productInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  productDetails: {
    flex: 1,
    marginRight: 16,
  },
  productName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  productCode: {
    color: '#6b7280',
    marginBottom: 2,
  },
  productCategory: {
    color: '#6b7280',
    marginBottom: 2,
  },
  productPrice: {
    color: '#059669',
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default ProductSelectionModal;
