import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text, Chip, Button, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Scheme } from '../../screens/management/api/scheme';

interface SchemeCardProps {
  scheme: Scheme;
  onViewDetails: (scheme: Scheme) => void;
  onEdit?: (scheme: Scheme) => void;
  onDelete?: (scheme: Scheme) => void;
  canEdit: boolean;
}

const SchemeCard: React.FC<SchemeCardProps> = ({
  scheme,
  onViewDetails,
  onEdit,
  onDelete,
  canEdit,
}) => {
  const theme = useTheme();

  // Format date for display
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get offer display text
  const getOfferText = (offer: 'TRIP' | 'GIFT'): string => {
    switch (offer) {
      case 'TRIP':
        return 'Trip Reward';
      case 'GIFT':
        return 'Gift Reward';
      default:
        return 'Reward';
    }
  };

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text variant="titleMedium" style={styles.title}>{scheme.name}</Text>
            <Chip
              mode="flat"
              style={[
                styles.statusChip,
                {
                  backgroundColor: scheme.status === 1
                    ? `${theme.colors.tertiary}20`
                    : '#e5e7eb'
                }
              ]}
            >
              <Text
                style={{
                  color: scheme.status === 1 ? theme.colors.tertiary : '#6b7280',
                  fontWeight: '500'
                }}
              >
                {scheme.status === 1 ? 'Active' : 'Inactive'}
              </Text>
            </Chip>
          </View>
          <Text variant="bodyMedium" style={styles.description}>{scheme.description}</Text>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Icon name="local-offer" size={16} color={theme.colors.primary} style={styles.detailIcon} />
            <Text style={styles.detailText}>{getOfferText(scheme.offer)}</Text>
          </View>

          <View style={styles.detailRow}>
            <Icon name="calendar-today" size={16} color="#6b7280" style={styles.detailIcon} />
            <Text style={styles.detailText}>Valid: {formatDate(scheme.startDate)} to {formatDate(scheme.endDate)}</Text>
          </View>

          <View style={styles.detailRow}>
            <Icon name="person" size={16} color="#6b7280" style={styles.detailIcon} />
            <Text style={styles.detailText}>User ID: {scheme.userId}</Text>
          </View>

          {scheme.purchaseAmount > 0 && (
            <View style={styles.detailRow}>
              <Icon name="shopping-cart" size={16} color="#6b7280" style={styles.detailIcon} />
              <Text style={styles.detailText}>Min. Purchase: ₹{scheme.purchaseAmount}</Text>
            </View>
          )}
        </View>
      </Card.Content>

      <Card.Actions style={styles.actions}>
        <Button
          mode="text"
          onPress={() => onViewDetails(scheme)}
          icon={({size, color}) => (
            <Icon name="visibility" size={size} color={color} />
          )}
          textColor={theme.colors.primary}
        >
          View Details
        </Button>

        {canEdit && onEdit && (
          <Button
            mode="text"
            onPress={() => onEdit(scheme)}
            icon={({size, color}) => (
              <Icon name="edit" size={size} color={color} />
            )}
            textColor="#0284C7"
          >
            Edit
          </Button>
        )}

        {canEdit && onDelete && (
          <Button
            mode="text"
            onPress={() => onDelete(scheme)}
            icon={({size, color}) => (
              <Icon name="delete" size={size} color={color} />
            )}
            textColor="#ef4444"
          >
            Delete
          </Button>
        )}
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  header: {
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  title: {
    fontWeight: 'bold',
    flex: 1,
  },
  statusChip: {
    borderRadius: 12,
    height: 28,
  },
  description: {
    color: '#6b7280',
  },
  detailsContainer: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailIcon: {
    marginRight: 8,
  },
  detailText: {
    color: '#4b5563',
    fontSize: 14,
  },
  actions: {
    justifyContent: 'flex-start',
  },
});

export default SchemeCard;
