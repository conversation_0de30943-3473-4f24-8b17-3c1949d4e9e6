import React, { useState } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  FlatList,
} from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  Searchbar,
  Button,
  Checkbox,
  useTheme,
  ActivityIndicator,
} from 'react-native-paper';
import { User } from '../../data/mockData';
import EmptyState from '../common/EmptyState';

interface UserSelectionModalProps {
  visible: boolean;
  onDismiss: () => void;
  users: User[];
  selectedUsers: User[];
  onSelectionChange: (users: User[]) => void;
  title: string;
  isLoading?: boolean;
  multiSelect?: boolean;
}

const UserSelectionModal: React.FC<UserSelectionModalProps> = ({
  visible,
  onDismiss,
  users,
  selectedUsers,
  onSelectionChange,
  title,
  isLoading = false,
  multiSelect = true,
}) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  // Filter users based on search query
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (user.email && user.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (user.phone && user.phone.includes(searchQuery))
  );

  // Check if user is selected
  const isUserSelected = (user: User): boolean => {
    return selectedUsers.some(selected => selected.id === user.id);
  };

  // Toggle user selection
  const toggleUserSelection = (user: User) => {
    if (multiSelect) {
      if (isUserSelected(user)) {
        // Remove user from selection
        onSelectionChange(selectedUsers.filter(selected => selected.id !== user.id));
      } else {
        // Add user to selection
        onSelectionChange([...selectedUsers, user]);
      }
    } else {
      // Single select mode
      onSelectionChange(isUserSelected(user) ? [] : [user]);
    }
  };

  // Select all users
  const selectAllUsers = () => {
    onSelectionChange(filteredUsers);
  };

  // Clear all selections
  const clearAllSelections = () => {
    onSelectionChange([]);
  };

  // Apply and close modal
  const handleApply = () => {
    onDismiss();
  };

  // Render user item
  const renderUserItem = ({ item }: { item: User }) => (
    <Surface style={styles.userItem} elevation={1}>
      <View style={styles.userInfo}>
        <View style={styles.userDetails}>
          <Text variant="titleSmall" style={styles.userName}>
            {item.name}
          </Text>
          {item.email && (
            <Text variant="bodySmall" style={styles.userEmail}>
              {item.email}
            </Text>
          )}
          {item.phone && (
            <Text variant="bodySmall" style={styles.userPhone}>
              {item.phone}
            </Text>
          )}
        </View>
        <Checkbox
          status={isUserSelected(item) ? 'checked' : 'unchecked'}
          onPress={() => toggleUserSelection(item)}
          theme={{ colors: { primary: theme.colors.primary } }}
        />
      </View>
    </Surface>
  );

  return (
    <Modal
      visible={visible}
      onRequestClose={onDismiss}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <Surface style={styles.container} elevation={4}>
        
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineSmall" style={styles.title}>
            {title}
          </Text>
          <IconButton
            icon="close"
            size={24}
            onPress={onDismiss}
          />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search users..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            iconColor={theme.colors.primary}
          />
        </View>

        {/* Bulk Actions */}
        {multiSelect && (
          <View style={styles.bulkActions}>
            <Text variant="bodyMedium" style={styles.selectedCount}>
              {selectedUsers.length} selected
            </Text>
            <View style={styles.bulkButtons}>
              <Button
                mode="text"
                onPress={selectAllUsers}
                disabled={filteredUsers.length === 0}
              >
                Select All
              </Button>
              <Button
                mode="text"
                onPress={clearAllSelections}
                disabled={selectedUsers.length === 0}
              >
                Clear All
              </Button>
            </View>
          </View>
        )}

        {/* Loading State */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading users...</Text>
          </View>
        )}

        {/* User List */}
        {!isLoading && (
          <>
            {filteredUsers.length === 0 ? (
              <EmptyState
                icon="account-search"
                message={searchQuery ? 'No users match your search' : 'No users available'}
              />
            ) : (
              <FlatList
                data={filteredUsers}
                renderItem={renderUserItem}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.userList}
                showsVerticalScrollIndicator={false}
              />
            )}
          </>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.footerButton}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleApply}
            style={styles.footerButton}
          >
            Apply ({selectedUsers.length})
          </Button>
        </View>

      </Surface>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontWeight: 'bold',
    color: '#374151',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: 'white',
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  bulkActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  selectedCount: {
    color: '#6b7280',
    fontWeight: '500',
  },
  bulkButtons: {
    flexDirection: 'row',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    color: '#6b7280',
  },
  userList: {
    padding: 16,
    paddingBottom: 80,
  },
  userItem: {
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  userInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  userDetails: {
    flex: 1,
    marginRight: 16,
  },
  userName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    color: '#6b7280',
    marginBottom: 2,
  },
  userPhone: {
    color: '#6b7280',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default UserSelectionModal;
