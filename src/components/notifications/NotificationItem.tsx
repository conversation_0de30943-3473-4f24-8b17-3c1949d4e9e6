import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Notification } from '../../data/mockData';
import { formatDistanceToNow } from 'date-fns';

interface NotificationItemProps {
  notification: Notification;
  onPress: (notification: Notification) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onPress }) => {
  // Get icon based on notification type
  const getIcon = () => {
    if (!notification?.type) return 'notifications';

    switch (notification.type) {
      case 'order':
        return 'shopping-cart';
      case 'user':
        return 'person-add';
      case 'catalog':
        return 'category';
      case 'scheme':
        return 'local-offer';
      case 'system':
        return 'info';
      default:
        return 'notifications';
    }
  };

  // Format the time
  const getFormattedTime = () => {
    try {
      if (!notification?.createdAt) return 'some time ago';
      return formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true });
    } catch (error) {
      return 'some time ago';
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        notification && notification.isRead === false ? styles.unread : null
      ]}
      onPress={() => onPress(notification)}
    >
      {notification?.fromUser?.avatar ? (
        <Image
          source={{ uri: notification?.fromUser?.avatar }}
          style={styles.avatar}
        />
      ) : (
        <View style={styles.iconContainer}>
          <Icon name={getIcon()} size={24} color="#6366f1" />
        </View>
      )}

      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={1}>
          {notification?.title || 'Notification'}
        </Text>
        <Text style={styles.message} numberOfLines={2}>
          {notification?.message || ''}
        </Text>
        <Text style={styles.time}>
          {getFormattedTime()}
        </Text>
      </View>

      {notification && notification.isRead === false && (
        <View style={styles.unreadIndicator} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    backgroundColor: 'white',
  },
  unread: {
    backgroundColor: '#f0f9ff',
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#e0e7ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 4,
  },
  time: {
    fontSize: 12,
    color: '#9ca3af',
  },
  unreadIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#6366f1',
    alignSelf: 'center',
    marginLeft: 8,
  },
});

export default NotificationItem;
