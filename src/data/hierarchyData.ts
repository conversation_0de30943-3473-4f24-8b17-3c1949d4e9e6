// Enhanced mock data with proper hierarchy
import { UserRole } from './mockData';

// User interface
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  avatar?: string;
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  password?: string;
}

// Super Stockist interface
export interface SuperStockist extends User {
  states: string[];
  distributors: string[]; // IDs of distributors
  creditLimit: number;
}

// Distributor interface
export interface Distributor extends User {
  region: string;
  superStockistId: string;
  retailers: string[]; // IDs of retailers
  creditLimit: number;
}

// Retailer interface
export interface Retailer extends User {
  location: string;
  distributorId: string;
  creditLimit: number;
}

// Create 2 Super Stockists
export const hierarchySuperStockists: SuperStockist[] = [
  {
    id: 'ss-1',
    name: 'North India Super Stockist',
    email: '<EMAIL>',
    phone: '9876543001',
    role: UserRole.SUPER_STOCKIST,
    avatar: 'https://randomuser.me/api/portraits/men/10.jpg',
    status: 'active',
    createdAt: '2023-01-15',
    states: ['Delhi', 'Uttar Pradesh', 'Haryana'],
    distributors: ['dist-1', 'dist-2', 'dist-3'],
    creditLimit: 1500000,
    password: 'ss1123'
  },
  {
    id: 'ss-2',
    name: 'South India Super Stockist',
    email: '<EMAIL>',
    phone: '9876543002',
    role: UserRole.SUPER_STOCKIST,
    avatar: 'https://randomuser.me/api/portraits/women/10.jpg',
    status: 'active',
    createdAt: '2023-01-20',
    states: ['Karnataka', 'Tamil Nadu', 'Kerala'],
    distributors: ['dist-4', 'dist-5', 'dist-6'],
    creditLimit: 1800000,
    password: 'ss2123'
  }
];

// Create 3 Distributors per Super Stockist (6 total)
export const hierarchyDistributors: Distributor[] = [
  // North India Super Stockist's Distributors
  {
    id: 'dist-1',
    name: 'Delhi Distributor',
    email: '<EMAIL>',
    phone: '9876543101',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/men/20.jpg',
    status: 'active',
    createdAt: '2023-02-10',
    region: 'Delhi NCR',
    superStockistId: 'ss-1',
    retailers: ['ret-1', 'ret-2', 'ret-3'],
    creditLimit: 500000,
    password: 'dist1123'
  },
  {
    id: 'dist-2',
    name: 'Lucknow Distributor',
    email: '<EMAIL>',
    phone: '9876543102',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/women/20.jpg',
    status: 'active',
    createdAt: '2023-02-15',
    region: 'Uttar Pradesh East',
    superStockistId: 'ss-1',
    retailers: ['ret-4', 'ret-5', 'ret-6'],
    creditLimit: 450000,
    password: 'dist2123'
  },
  {
    id: 'dist-3',
    name: 'Gurgaon Distributor',
    email: '<EMAIL>',
    phone: '9876543103',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/men/21.jpg',
    status: 'active',
    createdAt: '2023-02-20',
    region: 'Haryana',
    superStockistId: 'ss-1',
    retailers: ['ret-7', 'ret-8', 'ret-9'],
    creditLimit: 480000,
    password: 'dist3123'
  },

  // South India Super Stockist's Distributors
  {
    id: 'dist-4',
    name: 'Bangalore Distributor',
    email: '<EMAIL>',
    phone: '9876543104',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/women/21.jpg',
    status: 'active',
    createdAt: '2023-03-10',
    region: 'Karnataka',
    superStockistId: 'ss-2',
    retailers: ['ret-10', 'ret-11', 'ret-12'],
    creditLimit: 520000,
    password: 'dist4123'
  },
  {
    id: 'dist-5',
    name: 'Chennai Distributor',
    email: '<EMAIL>',
    phone: '9876543105',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
    status: 'active',
    createdAt: '2023-03-15',
    region: 'Tamil Nadu',
    superStockistId: 'ss-2',
    retailers: ['ret-13', 'ret-14', 'ret-15'],
    creditLimit: 490000,
    password: 'dist5123'
  },
  {
    id: 'dist-6',
    name: 'Kochi Distributor',
    email: '<EMAIL>',
    phone: '9876543106',
    role: UserRole.DISTRIBUTOR,
    avatar: 'https://randomuser.me/api/portraits/women/22.jpg',
    status: 'active',
    createdAt: '2023-03-20',
    region: 'Kerala',
    superStockistId: 'ss-2',
    retailers: ['ret-16', 'ret-17', 'ret-18'],
    creditLimit: 460000,
    password: 'dist6123'
  }
];

// Create 3 Retailers per Distributor (18 total)
export const hierarchyRetailers: Retailer[] = [
  // Delhi Distributor's Retailers
  {
    id: 'ret-1',
    name: 'Delhi Central Retailer',
    email: '<EMAIL>',
    phone: '9876543201',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/30.jpg',
    status: 'active',
    createdAt: '2023-04-05',
    location: 'Delhi Central',
    distributorId: 'dist-1',
    creditLimit: 200000,
    password: 'ret1123'
  },
  {
    id: 'ret-2',
    name: 'Delhi North Retailer',
    email: '<EMAIL>',
    phone: '9876543202',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/30.jpg',
    status: 'active',
    createdAt: '2023-04-10',
    location: 'Delhi North',
    distributorId: 'dist-1',
    creditLimit: 180000,
    password: 'ret2123'
  },
  {
    id: 'ret-3',
    name: 'Delhi South Retailer',
    email: '<EMAIL>',
    phone: '9876543203',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/31.jpg',
    status: 'active',
    createdAt: '2023-04-15',
    location: 'Delhi South',
    distributorId: 'dist-1',
    creditLimit: 190000,
    password: 'ret3123'
  },

  // Lucknow Distributor's Retailers
  {
    id: 'ret-4',
    name: 'Lucknow Central Retailer',
    email: '<EMAIL>',
    phone: '9876543204',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/31.jpg',
    status: 'active',
    createdAt: '2023-04-20',
    location: 'Lucknow Central',
    distributorId: 'dist-2',
    creditLimit: 170000
  },
  {
    id: 'ret-5',
    name: 'Lucknow East Retailer',
    email: '<EMAIL>',
    phone: '9876543205',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    status: 'active',
    createdAt: '2023-04-25',
    location: 'Lucknow East',
    distributorId: 'dist-2',
    creditLimit: 160000
  },
  {
    id: 'ret-6',
    name: 'Lucknow West Retailer',
    email: '<EMAIL>',
    phone: '9876543206',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
    status: 'active',
    createdAt: '2023-04-30',
    location: 'Lucknow West',
    distributorId: 'dist-2',
    creditLimit: 165000
  },

  // Gurgaon Distributor's Retailers
  {
    id: 'ret-7',
    name: 'Gurgaon Central Retailer',
    email: '<EMAIL>',
    phone: '9876543207',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/33.jpg',
    status: 'active',
    createdAt: '2023-05-05',
    location: 'Gurgaon Central',
    distributorId: 'dist-3',
    creditLimit: 185000
  },
  {
    id: 'ret-8',
    name: 'Gurgaon North Retailer',
    email: '<EMAIL>',
    phone: '9876543208',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    status: 'active',
    createdAt: '2023-05-10',
    location: 'Gurgaon North',
    distributorId: 'dist-3',
    creditLimit: 175000
  },
  {
    id: 'ret-9',
    name: 'Gurgaon South Retailer',
    email: '<EMAIL>',
    phone: '9876543209',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/34.jpg',
    status: 'active',
    createdAt: '2023-05-15',
    location: 'Gurgaon South',
    distributorId: 'dist-3',
    creditLimit: 180000
  },

  // Bangalore Distributor's Retailers
  {
    id: 'ret-10',
    name: 'Bangalore Central Retailer',
    email: '<EMAIL>',
    phone: '9876543210',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/34.jpg',
    status: 'active',
    createdAt: '2023-05-20',
    location: 'Bangalore Central',
    distributorId: 'dist-4',
    creditLimit: 195000
  },
  {
    id: 'ret-11',
    name: 'Bangalore East Retailer',
    email: '<EMAIL>',
    phone: '9876543211',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/35.jpg',
    status: 'active',
    createdAt: '2023-05-25',
    location: 'Bangalore East',
    distributorId: 'dist-4',
    creditLimit: 185000
  },
  {
    id: 'ret-12',
    name: 'Bangalore West Retailer',
    email: '<EMAIL>',
    phone: '9876543212',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/35.jpg',
    status: 'active',
    createdAt: '2023-05-30',
    location: 'Bangalore West',
    distributorId: 'dist-4',
    creditLimit: 190000
  },

  // Chennai Distributor's Retailers
  {
    id: 'ret-13',
    name: 'Chennai Central Retailer',
    email: '<EMAIL>',
    phone: '9876543213',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/36.jpg',
    status: 'active',
    createdAt: '2023-06-05',
    location: 'Chennai Central',
    distributorId: 'dist-5',
    creditLimit: 175000
  },
  {
    id: 'ret-14',
    name: 'Chennai North Retailer',
    email: '<EMAIL>',
    phone: '9876543214',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/36.jpg',
    status: 'active',
    createdAt: '2023-06-10',
    location: 'Chennai North',
    distributorId: 'dist-5',
    creditLimit: 165000
  },
  {
    id: 'ret-15',
    name: 'Chennai South Retailer',
    email: '<EMAIL>',
    phone: '9876543215',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/37.jpg',
    status: 'active',
    createdAt: '2023-06-15',
    location: 'Chennai South',
    distributorId: 'dist-5',
    creditLimit: 170000
  },

  // Kochi Distributor's Retailers
  {
    id: 'ret-16',
    name: 'Kochi Central Retailer',
    email: '<EMAIL>',
    phone: '9876543216',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/37.jpg',
    status: 'active',
    createdAt: '2023-06-20',
    location: 'Kochi Central',
    distributorId: 'dist-6',
    creditLimit: 160000
  },
  {
    id: 'ret-17',
    name: 'Kochi North Retailer',
    email: '<EMAIL>',
    phone: '9876543217',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/men/38.jpg',
    status: 'active',
    createdAt: '2023-06-25',
    location: 'Kochi North',
    distributorId: 'dist-6',
    creditLimit: 155000
  },
  {
    id: 'ret-18',
    name: 'Kochi South Retailer',
    email: '<EMAIL>',
    phone: '9876543218',
    role: UserRole.RETAILER,
    avatar: 'https://randomuser.me/api/portraits/women/38.jpg',
    status: 'active',
    createdAt: '2023-06-30',
    location: 'Kochi South',
    distributorId: 'dist-6',
    creditLimit: 150000
  }
];

// Combine all users for easy access
export const hierarchyUsers: User[] = [
  // Admin user
  {
    id: 'ooge-1',
    name: 'Ooge Admin',
    email: '<EMAIL>',
    phone: '9876543000',
    role: UserRole.OOGE_TEAM,
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    status: 'active',
    createdAt: '2023-01-01',
    password: 'admin123'
  },
  // Add all super stockists
  ...hierarchySuperStockists,
  // Add all distributors
  ...hierarchyDistributors,
  // Add all retailers
  ...hierarchyRetailers
];
