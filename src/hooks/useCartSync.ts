import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetCartByUserIdQuery } from '../screens/cart/cartApi/apiSlice';
import { setCartFromServer, clearCart } from '../redux/slices/cartSlice';
import { useUser } from '../context/UserContext';
import type { RootState } from '../redux/store';

export const useCartSync = () => {
  const dispatch = useDispatch();
  const { currentUser } = useUser();
  const localCartItems = useSelector((state: RootState) => state.cart.items);

  const {
    data: cartData,
    error: cartError,
    refetch: refetchCart,
    isLoading,
  } = useGetCartByUserIdQuery(Number(currentUser?.id), {
    skip: !currentUser?.id,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  useEffect(() => {
    console.log('🔄 [CART_SYNC] Effect triggered:', {
      hasCartData: !!cartData,
      hasError: !!cartError,
      localItemsCount: localCartItems.length,
      userId: currentUser?.id
    });

    if (!currentUser?.id) {
      console.log('🔄 [CART_SYNC] No user ID, clearing cart');
      dispatch(clearCart());
      return;
    }
  
    // Handle errors first
    if (cartError && 'status' in cartError) {
      const status = cartError.status;
      const errorMessage = cartError.data?.message?.toLowerCase() || '';
      
      console.log('🔄 [CART_SYNC] Cart error:', { status, errorMessage });
      
      if (status === 404 || status === 400) {
        if (errorMessage.includes('cart not found') || 
            errorMessage.includes('cart does not exist') ||
            errorMessage.includes('no cart found')) {
          console.log('🗑️ [CART_SYNC] Server cart not found, clearing local cart');
          dispatch(clearCart());
          return;
        }
      }
    }
    
    // Handle successful cart data
    if (cartData?.cartLine && Array.isArray(cartData.cartLine)) {
      const serverItems = cartData.cartLine.map((item: any) => ({
        id: item.productId,
        name: item.productName,
        price: `₹${item?.price || 0}`,
        quantity: item.quantity,
        image: item.image || '',
        variant: item.variantName,
        cartLineId: item.id,
        variantId: item.variantId
      }));
      
      console.log('🔄 [CART_SYNC] Syncing server cart to local:', {
        serverCount: serverItems.length,
        localCount: localCartItems.length
      });
      
      dispatch(setCartFromServer(serverItems));
    } 
    else if (cartData === null || (cartData && (!cartData.cartLine || cartData.cartLine.length === 0))) {
      // Server returned null/empty but we might have local items
      if (localCartItems.length > 0) {
        console.log('🗑️ [CART_SYNC] Server returned empty, clearing local cart');
        dispatch(clearCart());
      }
    }
  }, [cartData, cartError, dispatch, currentUser?.id]);

  return { 
    refetchCart, 
    isLoading, 
    cartError,
    serverCartCount: cartData?.cartLine?.length || 0,
    localCartCount: localCartItems.length
  };
};
