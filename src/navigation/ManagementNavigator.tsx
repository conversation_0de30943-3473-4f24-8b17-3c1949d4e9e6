import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../context/UserContext';
import { UserRole } from '../data/mockData';
import { Platform, KeyboardAvoidingView } from 'react-native';

// Import screens
import UserManagementScreen from '../screens/management/UserManagementScreen';
import PricingManagementScreen from '../screens/management/PricingManagementScreen';
import SchemeManagementScreen from '../screens/management/SchemeManagementScreen';
// import SuperStockistListScreen from '../screens/management/SuperStockistListScreen';
// import DistributorListScreen from '../screens/management/DistributorListScreen';
// import RetailerListScreen from '../screens/management/RetailerListScreen';
import LeaderboardScreen from '../screens/main/LeaderboardScreen';

// Define tab types
type TabConfig = {
  name: string;
  component: React.ComponentType<any>;
  icon: string;
  roles: UserRole[];
};

const ManagementNavigator = () => {
  const { currentUser } = useUser();
  const role = currentUser?.role || UserRole.PUBLIC;
  
  // Create a separate navigator for management tabs
  const ManagementTab = createBottomTabNavigator();
  
  // Define all possible tabs with role restrictions
  const allTabs: TabConfig[] = [
    {
      name: 'Users',
      component: UserManagementScreen,
      icon: 'people',
      roles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR]
    },
    // {
    //   name: 'Super Stockists',
    //   component: SuperStockistListScreen,
    //   icon: 'supervisor-account',
    //   roles: [UserRole.OOGE_TEAM]
    // },
    // {
    //   name: 'Distributors',
    //   component: DistributorListScreen,
    //   icon: 'groups',
    //   roles: [UserRole.OOGE_TEAM]
    // },
    // {
    //   name: 'Retailers',
    //   component: RetailerListScreen,
    //   icon: 'store',
    //   roles: [UserRole.OOGE_TEAM]
    // },
    {
      name: 'Pricing',
      component: PricingManagementScreen,
      icon: 'attach-money',
      roles: []
    },
    {
      name: 'Schemes',
      component: SchemeManagementScreen,
      icon: 'local-offer',
      roles: []
    },
    {
      name: 'Leaderboard',
      component: LeaderboardScreen,
      icon: 'leaderboard',
      roles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR]
    },
  ];
  
  // Filter tabs based on user role
  const getVisibleTabs = () => {
    return allTabs.filter(tab => tab.roles.includes(role));
  };
  
  // Get visible tabs for current user
  const visibleTabs = getVisibleTabs();
  
  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
    <ManagementTab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#6366f1',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarHideOnKeyboard: true,
      }}
      >
      {visibleTabs.map((tab) => (
        <ManagementTab.Screen
          key={tab.name}
          name={tab.name}
          component={tab.component}
          options={{
            tabBarIcon: ({ color }) => <Icon name={tab.icon} size={24} color={color} />
          }}
        />
      ))}
    </ManagementTab.Navigator>
    </KeyboardAvoidingView>
  );
};

export default ManagementNavigator;
