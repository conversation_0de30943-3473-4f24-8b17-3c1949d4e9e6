import React, { useState } from 'react';
import { BottomNavigation, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useUser } from '../context/UserContext';
import { UserRole } from '../data/mockData';

// Import screens
import UserManagementScreen from '../screens/management/UserManagementScreen';
import PricingManagementScreen from '../screens/management/PricingManagementScreen';
import SchemeManagementScreen from '../screens/management/SchemeManagementScreen';
import OffersManagementScreen from '../screens/management/OffersManagementScreen';
import SuperStockistListScreen from '../screens/management/SuperStockistListScreen';
import DistributorListScreen from '../screens/management/DistributorListScreen';
import RetailerListScreen from '../screens/management/RetailerListScreen';
import { KeyboardAvoidingView,Platform } from 'react-native';

const ManagementTabNavigator = () => {
  const [index, setIndex] = useState(0);
  const { currentUser } = useUser();
  const theme = useTheme();

  const role = currentUser?.role || UserRole.PUBLIC;

  // Define routes based on user role
  const getRoutesByRole = () => {
    const commonRoutes = [
      {
        key: 'users',
        title: 'Users',
        focusedIcon: 'people',
        unfocusedIcon: 'people-outline',
      },
      {
        key: 'pricing',
        title: 'Pricing',
        focusedIcon: 'attach-money',
        unfocusedIcon: 'attach-money',
      },
      {
        key: 'schemes',
        title: 'Schemes',
        focusedIcon: 'local-offer',
        unfocusedIcon: 'local-offer',
      },
    ];

    // Add offers tab for roles that can manage offers
    if ([UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(role)) {
      commonRoutes.push({
        key: 'offers',
        title: 'Offers',
        focusedIcon: 'campaign',
        unfocusedIcon: 'campaign',
      });
    }

    return commonRoutes;
  };

  const routes = getRoutesByRole();

  // Define screens based on user role
  const getUsersScreen = () => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return <SuperStockistListScreen />;
      case UserRole.SUPER_STOCKIST:
        return <DistributorListScreen />;
      case UserRole.DISTRIBUTOR:
        return <RetailerListScreen />;
      default:
        return <UserManagementScreen />;
    }
  };

  const getSceneMap = () => {
    const scenes: any = {
      users: getUsersScreen,
      pricing: () => <PricingManagementScreen />,
      schemes: () => <SchemeManagementScreen />,
    };

    // Add offers scene for roles that can manage offers
    if ([UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(role)) {
      scenes.offers = () => <OffersManagementScreen />;
    }

    return BottomNavigation.SceneMap(scenes);
  };

  const renderScene = getSceneMap();

  return (
    <KeyboardAvoidingView
    style={{ flex: 1 }}
    behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
  >
    <BottomNavigation
      navigationState={{ index, routes }}
      onIndexChange={setIndex}
      renderScene={renderScene}
      barStyle={{ backgroundColor: 'white' }}
      activeColor={theme.colors.primary}
      inactiveColor="#9ca3af"
      renderIcon={({ route, focused, color }) => (
        <Icon
          name={focused ? route.focusedIcon : route.unfocusedIcon || route.focusedIcon}
          size={24}
          color={color}
        />
      )}
    />
    </KeyboardAvoidingView>
  );
};

export default ManagementTabNavigator;
