import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CartItem {
  id: number;
  name: string;
  price: string;
  quantity: number;
  image: string;
  variant?: string;
  cartLineId?: number;
  variantId?: number;
}

interface Coupon {
  id: string;
  code: string;
  discount: number;
  description: string;
}

interface CartState {
  items: CartItem[];
  subtotal: number;
  shippingCost: number;
  taxAmount: number;
  total: number;
  selectedCoupon?: Coupon;
  paymentMethod: 'online' | 'cod' | null;
  lastUpdated: number; // Add timestamp for debugging
}

const initialState: CartState = {
  items: [],
  subtotal: 0,
  shippingCost: 0,
  taxAmount: 0,
  total: 0,
  selectedCoupon: undefined,
  paymentMethod: null,
  lastUpdated: Date.now(),
};
const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const existingItem = state.items.find(
        item => item.id === action.payload.id && item.variant === action.payload.variant
      );

      if (existingItem) {
        existingItem.quantity += action.payload.quantity;
      } else {
        state.items.push(action.payload);
      }

      state.lastUpdated = Date.now();
      cartSlice.caseReducers.updateTotal(state);
    },

    removeFromCart: (state, action: PayloadAction<{ id: number; variant?: string }>) => {
      state.items = state.items.filter(
        item => !(item.id === action.payload.id && item.variant === action.payload.variant)
      );

      state.lastUpdated = Date.now();
      cartSlice.caseReducers.updateTotal(state);
    },

    updateQuantity: (state, action: PayloadAction<{ id: number; quantity: number; variant?: string }>) => {
      const item = state.items.find(
        item => item.id === action.payload.id && item.variant === action.payload.variant
      );
      if (item) {
        item.quantity = Math.max(1, action.payload.quantity);
      }

      state.lastUpdated = Date.now();
      cartSlice.caseReducers.updateTotal(state);
    },

    updateTotal: (state) => {
      state.subtotal = state.items.reduce((sum, item) => {
        try {
          const priceString = item.price.replace(/[^\d.]/g, '');
          const price = parseFloat(priceString);
          if (isNaN(price)) {
            console.error('Invalid price format:', item.price);
            return sum;
          }
          return sum + (price * item.quantity);
        } catch (error) {
          console.error('Error calculating item price:', error);
          return sum;
        }
      }, 0);

      state.shippingCost = state.subtotal >= 1000 ? 0 : 40;
      state.taxAmount = state.subtotal * 0.18;
      const discount = state.selectedCoupon ? state.selectedCoupon.discount : 0;
      state.total = state.subtotal + state.shippingCost + state.taxAmount - discount;
    },

    clearCart: (state) => {
      console.log('🗑️ [REDUX] Clearing cart - items before:', state.items.length);
      state.items = [];
      state.subtotal = 0;
      state.shippingCost = 0;
      state.taxAmount = 0;
      state.total = 0;
      state.selectedCoupon = undefined;
      state.paymentMethod = null;
      state.lastUpdated = Date.now();
      console.log('🗑️ [REDUX] Cart cleared - items after:', state.items.length);
    },

    applyCoupon: (state, action: PayloadAction<Coupon>) => {
      state.selectedCoupon = action.payload;
      state.lastUpdated = Date.now();
      cartSlice.caseReducers.updateTotal(state);
    },

    removeCoupon: (state) => {
      state.selectedCoupon = undefined;
      state.lastUpdated = Date.now();
      cartSlice.caseReducers.updateTotal(state);
    },

    setPaymentMethod: (state, action: PayloadAction<'online' | 'cod'>) => {
      state.paymentMethod = action.payload;
      state.lastUpdated = Date.now();
    },

    setCartFromServer: (state, action: PayloadAction<CartItem[]>) => {
      console.log('🔄 [REDUX] Setting cart from server - items:', action.payload.length);
      state.items = action.payload;
      state.lastUpdated = Date.now();
      cartSlice.caseReducers.updateTotal(state);
      console.log('🔄 [REDUX] Cart set from server - final items:', state.items.length);
    },
  }
});

export const { 
  addToCart, 
  removeFromCart, 
  updateQuantity, 
  clearCart, 
  updateTotal, 
  applyCoupon, 
  removeCoupon, 
  setPaymentMethod, 
  setCartFromServer 
} = cartSlice.actions;

export default cartSlice.reducer;
