import { configureStore } from '@reduxjs/toolkit';
import cartReducer from './slices/cartSlice';
import addressReducer from './slices/addressSlice';
import { apiSlice } from '../services/api/apiSlice';
import { bannerApi } from '../components/Home/api/bannerslice';
import { cartApi } from '../screens/cart/cartApi/apiSlice';
import {PromotionalApi} from '../components/Home/api/Promotionalslice';
import { managementApi } from '../screens/management/api/apiSlice';
import { offersApi } from '../screens/management/api/offersApi';
import { schemeApi } from '../screens/management/api/scheme';
import { setupListeners } from '@reduxjs/toolkit/query';
import { pricingApi } from '../screens/management/api/price';

export const store = configureStore({
  reducer: {
    cart: cartReducer,
    address: addressReducer,
    [apiSlice.reducerPath]: apiSlice.reducer,
    [bannerApi.reducerPath]: bannerApi.reducer,
    [cartApi.reducerPath]: cartApi.reducer,
    [PromotionalApi.reducerPath]: PromotionalApi.reducer,
    [managementApi.reducerPath]: managementApi.reducer,
    [offersApi.reducerPath]: offersApi.reducer,
    [schemeApi.reducerPath]: schemeApi.reducer,
    [pricingApi.reducerPath]: pricingApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE','schemeApi/executeMutation/pending',
          'schemeApi/executeMutation/fulfilled',
          'schemeApi/executeQuery/pending',
          'schemeApi/executeQuery/fulfilled',],
      },
    })
      .concat(apiSlice.middleware)
      .concat(bannerApi.middleware)
      .concat(cartApi.middleware)
      .concat(PromotionalApi.middleware)
      .concat(managementApi.middleware)
      .concat(offersApi.middleware)
      .concat(schemeApi.middleware)
      .concat(pricingApi.middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
