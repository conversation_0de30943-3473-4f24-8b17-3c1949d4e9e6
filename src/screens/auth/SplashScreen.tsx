import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Image, ActivityIndicator, StatusBar } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useUser } from '../../context/UserContext';

const SplashScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const { setCurrentUser } = useUser();

  useEffect(() => {
    // Check for stored user credentials
    const checkAuth = async () => {
      try {
        const storedUser = await AsyncStorage.getItem('user');

        if (storedUser) {
          // If user data exists, restore the session
          const userData = JSON.parse(storedUser);
          setCurrentUser(userData);
          navigation.replace('MainApp');
        } else {
          // No stored credentials, navigate to Login screen
          navigation.replace('MainApp');
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        // On error, navigate to Login screen
        navigation.replace('MainApp');
      }
    };

    // Add a slight delay to show the splash screen
    const timer = setTimeout(() => {
      checkAuth();
    }, 2000);

    return () => clearTimeout(timer);
  }, [navigation, setCurrentUser]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1f2937" />
      <Image
        source={require('../../assets/oogelife_logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />
      <Text style={styles.title}>OOGE</Text>
      <Text style={styles.subtitle}>B2B Distribution Platform</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#4B4B4B', // dark gray background
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  logo: {
    width: 140,
    height: 140,
    marginBottom: 20,
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFD700',
    letterSpacing: 2,
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 16,
    color: '#D1D5DB', // light gray text
    marginBottom: 32,
    textAlign: 'center',
  },
});

export default SplashScreen;
