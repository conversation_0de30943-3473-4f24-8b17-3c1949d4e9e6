// import React, { useEffect, useMemo, useState } from 'react';
// import { View, ScrollView, Animated, Alert, ActivityIndicator, Text } from 'react-native';
// import { useSelector, useDispatch } from 'react-redux';
// import { useNavigation, useFocusEffect } from '@react-navigation/native';
// import type { RootState } from '../../redux/store';
// import { setSelectedAddress } from '../../redux/slices/addressSlice';
// // Import components
// import CartHeader from './components/CartHeader';
// import CartItem from './components/CartItem';
// import AddressBook from './components/AddressBook';
// import OrderSummary from './components/OrderSummary';
// import EmptyCart from './components/EmptyCart';

// // Import styles
// import { styles } from './styles/cartStyles';
// import { useUser } from '../../context/UserContext';
// import { 
//   useGetCartByUserIdQuery, 
//   useDeleteCartItemMutation, 
//   useGetAddressesByUserIdQuery, 
//   useCreateOrderMutation,
//   // useClearCartMutation,
//   cartApi
// } from './cartApi/apiSlice';

// const CartScreen = () => {
//   const navigation = useNavigation<any>();
//   const dispatch = useDispatch();
//   const { selectedAddressId } = useSelector((state: RootState) => state.address);
//   const [isLoading, setIsLoading] = useState(false);
//   const fadeAnim = React.useRef(new Animated.Value(0)).current;
//   const { currentUser } = useUser();

//   // API queries and mutations
//   const {
//     data: cartData,
//     isLoading: isCartLoading,
//     error: cartError,
//     refetch: refetchCart,
//   } = useGetCartByUserIdQuery(Number(currentUser?.id), {
//     skip: !currentUser?.id,
//     refetchOnMountOrArgChange: true,
//     refetchOnFocus: true,
//     refetchOnReconnect: true,
//   });

//   const {
//     data: apiAddresses = [],
//     isLoading: isAddressesLoading,
//     error: addressesError,
//     refetch: refetchAddresses,
//   } = useGetAddressesByUserIdQuery(Number(currentUser?.id), { 
//     skip: !currentUser?.id 
//   });

//   const [createOrder, { isLoading: isOrderCreating }] = useCreateOrderMutation();
//   const [deleteCartItem, { isLoading: isDeleting }] = useDeleteCartItemMutation();
//   // const [clearCartAPI, { isLoading: isClearingCart }] = useClearCartMutation();

//   // Transform API addresses to component format
//   const mappedAddresses = useMemo(() => {
//     return apiAddresses.map(addr => ({
//       id: String(addr.id),
//       type: addr.type,
//       name: addr.line1,
//       street: [addr.line1, addr.line2, addr.landmark].filter(Boolean).join(', '),
//       city: addr.city,
//       state: addr.state,
//       pincode: addr.pincode,
//       isDefault: addr.isPrimaryAddress === 1,
//     }));
//   }, [apiAddresses]);

//   // Transform cart data to display items
//   const displayItems = useMemo(() => {
//     if (!cartData?.cartLine || !Array.isArray(cartData.cartLine)) {
//       return [];
//     }


//     return cartData.cartLine.map(item => ({
//       id: item.productId,
//       name: item.productName,
//       price: `₹${item?.price || 0}`,
//       discountPrice: item.discountPrice,
//       discountPercentage: item.discountPercentage,
//       sellingPrice: item.sellingPrice,
//       quantity: item.quantity,
//       image: item.image || '',
//       variant: item.variantName,
//       cartLineId: item.id,
//       variantId: item.variantId
//     }));
//   }, [cartData]);

//   // Calculate totals from API data
//   const cartTotals = useMemo(() => {
//     if (!cartData) {
//       return {
//         subtotal: 0,
//         shippingCost: 0,
//         discount: 0,
//         total: 0
//       };
//     }

//     return {
//       subtotal: cartData.totalAmount || 0,
//       shippingCost: cartData.shippingCharges || 0,
//       discount: cartData.totalDiscountPrice || 0,
//       total: cartData.totalSellingPrice || 0
//     };
//   }, [cartData]);

//   // Address error state
//   const [addressError, setAddressError] = useState<string>('');

//   // Auto-select primary address when addresses are loaded
//   useEffect(() => {
//     if (mappedAddresses.length > 0 && !selectedAddressId) {
//       const primaryAddress = mappedAddresses.find(addr => addr.isDefault);
//       if (primaryAddress) {
//         console.log('Auto-selecting primary address:', primaryAddress.id);
//         dispatch(setSelectedAddress(primaryAddress.id));
//         setAddressError('');
//       }
//     }
//   }, [mappedAddresses, selectedAddressId, dispatch]);

//   // Clear address error when address is selected
//   useEffect(() => {
//     if (selectedAddressId) {
//       setAddressError('');
//     }
//   }, [selectedAddressId]);

//   // Refetch cart when screen comes into focus
//   useFocusEffect(
//     React.useCallback(() => {
//       if (currentUser?.id) {
//         console.log('🔄 [CART_SCREEN] Screen focused, refetching cart');
//         refetchCart();
//       }
//     }, [currentUser?.id, refetchCart])
//   );

//   useEffect(() => {
//     Animated.timing(fadeAnim, {
//       toValue: 1,
//       duration: 300,
//       useNativeDriver: true,
//     }).start();
//   }, [fadeAnim]);

//   const handleRemoveItem = async (id: number, variant?: string, cartLineId?: number) => {
//     Alert.alert(
//       "Remove Item",
//       "Are you sure you want to remove this item?",
//       [
//         { text: "Cancel", style: "cancel" },
//         {
//           text: "Remove",
//           onPress: async () => {
//             if (!cartLineId || !currentUser?.id || !cartData) {
//               Alert.alert("Error", "Unable to remove item. Please try again.");
//               return;
//             }

//             try {
//               const serverItem = displayItems.find(item => item.cartLineId === cartLineId);
//               if (!serverItem) {
//                 Alert.alert("Error", "Item not found.");
//                 return;
//               }

//               console.log('Deleting cart item with params:', {
//                 cartId: cartData.id,
//                 productId: id,
//                 variantId: serverItem.variantId
//               });

//               await deleteCartItem({
//                 cartId: cartData.id,
//                 productId: id,
//                 variantId: serverItem.variantId
//               }).unwrap();

//               console.log('✅ API deletion successful');
              
//               // Refetch cart to get updated data
//               refetchCart();
              
//             } catch (error: any) {
//               console.error('❌ Error deleting cart item:', error);
              
//               // Check if it's a "cart not found" error
//               const isCartNotFoundError = (
//                 error?.status === 400 || 
//                 error?.status === 404
//               ) && (
//                 error?.data?.message?.toLowerCase().includes('cart not found') ||
//                 error?.data?.message?.toLowerCase().includes('cart does not exist')
//               );

//               if (isCartNotFoundError) {
//                 console.log('🗑️ Cart not found during deletion - refetching cart');
//                 refetchCart();
//               } else {
//                 Alert.alert("Error", "Failed to remove item. Please try again.");
//               }
//             }
//           },
//         }
//       ]
//     );
//   };

//   const handleUpdateQuantity = async (id: number, quantity: number, variant?: string, cartLineId?: number) => {
//     // For quantity updates, you'll need to implement an update quantity API mutation
//     // For now, just refetch the cart
//     console.log('Update quantity called:', { id, quantity, variant, cartLineId });
//     // You would call an updateCartItemQuantity mutation here
//     // await updateCartItemQuantity({ cartId: cartData.id, productId: id, variantId, quantity }).unwrap();
//     refetchCart();
//   };

//   const handleClearCart = () => {
//     Alert.alert(
//       "Clear Cart",
//       "Are you sure you want to clear your cart?",
//       [
//         { text: "Cancel", style: "cancel" },
//         {
//           text: "Clear",
//           onPress: async () => {
//             if (!currentUser?.id || !cartData) {
//               return;
//             }

//             try {
//               // await clearCartAPI(cartData.id).unwrap();
//               console.log('✅ Cart cleared successfully');
//               refetchCart();
//             } catch (error: any) {
//               console.error('❌ Error clearing cart:', error);
//               Alert.alert("Error", "Failed to clear cart. Please try again.");
//             }
//           },
//         }
//       ]
//     );
//   };

//   const handleBuyNow = async () => {
//     if (!selectedAddressId) {
//       setAddressError('Please select a delivery address to place your order');
      
//       Alert.alert(
//         "Address Required", 
//         "Please select a delivery address to continue with your order.",
//         [
//           {
//             text: "Add Address",
//             onPress: () => navigation.navigate('AddAddress', {
//               returnScreen: 'Cart',
//               onAddressAdded: () => {
//                 refetchAddresses();
//               }
//             })
//           },
//           {
//             text: "Cancel",
//             style: "cancel"
//           }
//         ]
//       );
//       return;
//     }

//     const selectedAddress = mappedAddresses.find(addr => addr.id === selectedAddressId);
//     if (!selectedAddress) {
//       setAddressError('Selected address is no longer available. Please select another address.');
//       dispatch(setSelectedAddress(''));
//       return;
//     }

//     if (!displayItems.length) {
//       Alert.alert("Error", "Your cart is empty.");
//       return;
//     }

//     setIsLoading(true);

//     try {
//       const payload = {
//         userId: currentUser?.id,
//         userAddressId: selectedAddressId,
//         items: displayItems.map(item => ({
//           productId: item.id,
//           variantId: item.variantId,
//           quantity: item.quantity,
//           price: Number(item.sellingPrice?.toString().replace(/[^\d.]/g, '')) || 0
//         }))
//       };

//       console.log('🛒 [CART] Creating order with payload:', payload);

//       await createOrder(payload).unwrap();
//       console.log('✅ [CART] Order created successfully');

//       setAddressError('');

//       console.log('🎉 [CART] Order placed, navigating to ThankYou');
//       navigation.navigate('ThankYou');

//     } catch (error: any) {
//       console.error('❌ [CART] Order creation failed:', error);
//       Alert.alert('Order Failed',`status code ${error?.status}  ${error?.data?.data}` || 'Failed to place order. Please try again.');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const handleSelectAddress = (address: any) => {
//     dispatch(setSelectedAddress(address.id));
//     setAddressError('');
//   };

//   const handleAddNewAddress = () => {
//     navigation.navigate('AddAddress', {
//       returnScreen: 'Cart',
//       onAddressAdded: () => {
//         console.log('Address added, refreshing addresses...');
//         refetchAddresses();
//       }
//     });
//   };

//   const handleEditAddress = (addressId: string) => {
//     navigation.navigate('EditAddress', {
//       addressId,
//       returnScreen: 'Cart',
//       onAddressUpdated: () => {
//         console.log('Address updated, refreshing addresses...');
//         refetchAddresses();
//       }
//     });
//   };

//   // Loading state
//   if (!currentUser?.id || isCartLoading) {
//     return (
//       <View style={styles.loadingContainer}>
//         <ActivityIndicator size="large" color="#6366f1" />
//         <Text style={styles.loadingText}>Loading your cart...</Text>
//       </View>
//     );
//   }

//   // Handle cart errors
//   if (cartError) {
//     console.error('Cart error:', cartError);
//     if ('status' in cartError && (cartError.status === 404 || cartError.status === 400)) {
//       const errorMessage = cartError.data?.message?.toLowerCase() || '';
//       if (errorMessage.includes('cart not found') || errorMessage.includes('cart does not exist')) {
//         return <EmptyCart fadeAnim={fadeAnim} />;
//       }
//     }
//   }

//   // Empty cart
//   if (!displayItems || displayItems.length === 0) {
//     return <EmptyCart fadeAnim={fadeAnim} />;
//   }

//   return (
//     <View style={styles.container}>
//       <CartHeader
//         itemCount={displayItems.length}
//         onClearCart={handleClearCart}
//       />

//       {isLoading ? (
//         <View style={styles.loadingContainer}>
//           <ActivityIndicator size="large" color="#6366f1" />
//         </View>
//       ) : (
//         <ScrollView style={styles.scrollView}>
//           <Animated.View style={{ opacity: fadeAnim }}>
//             <View style={styles.cartItemsContainer}>
//               {displayItems.map((item, index) => (
//                 <CartItem
//                   key={`${item.id}-${item.variant || 'default'}-${item.cartLineId || 'local'}`}
//                   item={item}
//                   onRemove={(id, variant, cartLineId) => handleRemoveItem(id, variant, cartLineId)}
//                   onUpdateQuantity={(id, quantity, variant) =>
//                     handleUpdateQuantity(id, quantity, variant, item.cartLineId)
//                   }
//                   isLast={index === displayItems.length - 1}
//                 />
//               ))}
//             </View>

//             <AddressBook
//               addresses={mappedAddresses}
//               selectedAddress={selectedAddressId}
//               onSelectAddress={handleSelectAddress}
//               onAddNewAddress={handleAddNewAddress}
//               onEditAddress={handleEditAddress}
//               error={addressError}
//               isLoading={isAddressesLoading}
//             />

//             <OrderSummary
//               subtotal={cartTotals.subtotal}
//               shippingCost={cartTotals.shippingCost}
//               discount={cartTotals.discount}
//               total={cartTotals.total}
//               items={displayItems}
//               onBuyNow={handleBuyNow}
//               isLoading={isLoading || isOrderCreating}
//               selectedAddress={selectedAddressId}
//               addressError={addressError}
//             />
//             <View style={styles.bottomPadding} />
//           </Animated.View>
//         </ScrollView>
//       )}
//     </View>
//   );
// };

// export default CartScreen;



import React, { useEffect, useMemo, useState } from 'react';
import { View, ScrollView, Animated, Alert, ActivityIndicator, Text } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import type { RootState } from '../../redux/store';
import { setSelectedAddress } from '../../redux/slices/addressSlice';
import CartHeader from './components/CartHeader';
import CartItem from './components/CartItem';
import AddressBook from './components/AddressBook';
import OrderSummary from './components/OrderSummary';
import EmptyCart from './components/EmptyCart';
import { styles } from './styles/cartStyles';
import { useUser } from '../../context/UserContext';
import { 
  useGetCartByUserIdQuery, 
  useDeleteCartItemMutation, 
  useGetAddressesByUserIdQuery, 
  useCreateOrderMutation,
  useUpdateCartItemQuantityMutation,
  cartApi
} from './cartApi/apiSlice';

const CartScreen = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const { selectedAddressId } = useSelector((state: RootState) => state.address);
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const { currentUser } = useUser();

  const {
    data: cartData,
    isLoading: isCartLoading,
    error: cartError,
    refetch: refetchCart,
  } = useGetCartByUserIdQuery(Number(currentUser?.id), {
    skip: !currentUser?.id,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: apiAddresses = [],
    isLoading: isAddressesLoading,
    error: addressesError,
    refetch: refetchAddresses,
  } = useGetAddressesByUserIdQuery(Number(currentUser?.id), { 
    skip: !currentUser?.id 
  });

  const [createOrder, { isLoading: isOrderCreating }] = useCreateOrderMutation();
  const [deleteCartItem, { isLoading: isDeleting }] = useDeleteCartItemMutation();
  const [updateCartItemQuantity, { isLoading: isUpdatingQuantity }] = useUpdateCartItemQuantityMutation();

  const mappedAddresses = useMemo(() => {
    return apiAddresses.map(addr => ({
      id: String(addr.id),
      type: addr.type,
      name: addr.line1,
      street: [addr.line1, addr.line2, addr.landmark].filter(Boolean).join(', '),
      city: addr.city,
      state: addr.state,
      pincode: addr.pincode,
      isDefault: addr.isPrimaryAddress === 1,
    }));
  }, [apiAddresses]);

  const displayItems = useMemo(() => {
    if (!cartData?.cartLine || !Array.isArray(cartData.cartLine)) {
      return [];
    }

    return cartData.cartLine.map(item => ({
      id: item.productId,
      name: item.productName,
      price: `₹${item?.price || 0}`,
      discountPrice: item.discountPrice,
      discountPercentage: item.discountPercentage,
      sellingPrice: item.sellingPrice,
      quantity: item.quantity,
      image: item.image || '',
      variant: item.variantName,
      variantName: item.variantName,
      cartLineId: item.id,
      variantId: item.variantId
    }));
  }, [cartData]);

  const cartTotals = useMemo(() => {
    if (!cartData) {
      return {
        subtotal: 0,
        shippingCost: 0,
        discount: 0,
        total: 0
      };
    }

    return {
      subtotal: cartData.totalAmount || 0,
      shippingCost: cartData.shippingCharges || 0,
      discount: cartData.totalDiscountPrice || 0,
      total: cartData.totalSellingPrice || 0
    };
  }, [cartData]);

  const [addressError, setAddressError] = useState<string>('');

  useEffect(() => {
    if (mappedAddresses.length > 0 && !selectedAddressId) {
      const primaryAddress = mappedAddresses.find(addr => addr.isDefault);
      if (primaryAddress) {
        console.log('Auto-selecting primary address:', primaryAddress.id);
        dispatch(setSelectedAddress(primaryAddress.id));
        setAddressError('');
      }
    }
  }, [mappedAddresses, selectedAddressId, dispatch]);

  useEffect(() => {
    if (selectedAddressId) {
      setAddressError('');
    }
  }, [selectedAddressId]);

  useFocusEffect(
    React.useCallback(() => {
      if (currentUser?.id) {
        console.log('🔄 [CART_SCREEN] Screen focused, refetching cart');
        refetchCart();
      }
    }, [currentUser?.id, refetchCart])
  );

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const handleRemoveItem = async (id: number, variant?: string, cartLineId?: number) => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          onPress: async () => {
            if (!cartLineId || !currentUser?.id || !cartData) {
              Alert.alert("Error", "Unable to remove item. Please try again.");
              return;
            }

            try {
              const serverItem = displayItems.find(item => item.cartLineId === cartLineId);
              if (!serverItem) {
                Alert.alert("Error", "Item not found.");
                return;
              }

              console.log('Deleting cart item with params:', {
                cartId: cartData.id,
                productId: id,
                variantId: serverItem.variantId
              });

              await deleteCartItem({
                cartId: cartData.id,
                productId: id,
                variantId: serverItem.variantId
              }).unwrap();

              console.log('✅ API deletion successful');
              refetchCart();
              
            } catch (error: any) {
              console.error('❌ Error deleting cart item:', error);
              
              const isCartNotFoundError = (
                error?.status === 400 || 
                error?.status === 404
              ) && (
                error?.data?.message?.toLowerCase().includes('cart not found') ||
                error?.data?.message?.toLowerCase().includes('cart does not exist')
              );

              if (isCartNotFoundError) {
                console.log('🗑️ Cart not found during deletion - refetching cart');
                refetchCart();
              } else {
                Alert.alert("Error", "Failed to remove item. Please try again.");
              }
            }
          },
        }
      ]
    );
  };

  const handleUpdateQuantity = async (id: number, quantity: number, variant?: string, cartLineId?: number) => {
    if (!cartData || !currentUser?.id || !cartLineId || isUpdatingQuantity) {
      console.log('🚫 [CART] Update blocked - missing data or already updating');
      return;
    }

    const serverItem = displayItems.find(item => item.cartLineId === cartLineId);
    if (!serverItem) {
      console.error('Server item not found for cartLineId:', cartLineId);
      return;
    }

    try {
      console.log('🔄 [CART] Updating quantity with params:', {
        cartId: cartData.id,
        userId: Number(currentUser.id),
        productId: id,
        variantId: serverItem.variantId,
        quantity: quantity
      });

      const result = await updateCartItemQuantity({
        cartId: cartData.id,
        userId: Number(currentUser.id),
        productId: id,
        variantId: serverItem.variantId,
        quantity: quantity
      }).unwrap();

      console.log('✅ [CART] Quantity updated successfully:', result);
      
    } catch (error: any) {
      console.error('❌ [CART] Error updating quantity:', error);
      console.error('❌ [CART] Error details:', error.data);
      Alert.alert("Error", `Failed to update quantity: ${error.data?.message || error.message}`);
    }
  };

  const handleClearCart = () => {
    Alert.alert(
      "Clear Cart",
      "Are you sure you want to clear your cart?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          onPress: async () => {
            if (!currentUser?.id || !cartData) {
              return;
            }

            try {
              console.log('✅ Cart cleared successfully');
              refetchCart();
            } catch (error: any) {
              console.error('❌ Error clearing cart:', error);
              Alert.alert("Error", "Failed to clear cart. Please try again.");
            }
          },
        }
      ]
    );
  };

  const handleBuyNow = async () => {
    if (!selectedAddressId) {
      setAddressError('Please select a delivery address to place your order');
      
      Alert.alert(
        "Address Required", 
        "Please select a delivery address to continue with your order.",
        [
          {
            text: "Add Address",
            onPress: () => navigation.navigate('AddAddress', {
              returnScreen: 'Cart',
              onAddressAdded: () => {
                refetchAddresses();
              }
            })
          },
          {
            text: "Cancel",
            style: "cancel"
          }
        ]
      );
      return;
    }

    const selectedAddress = mappedAddresses.find(addr => addr.id === selectedAddressId);
    if (!selectedAddress) {
      setAddressError('Selected address is no longer available. Please select another address.');
      dispatch(setSelectedAddress(''));
      return;
    }

    if (!displayItems.length) {
      Alert.alert("Error", "Your cart is empty.");
      return;
    }

    setIsLoading(true);

    try {
      const payload = {
        userId: currentUser?.id,
        userAddressId: selectedAddressId,
        items: displayItems.map(item => ({
          productId: item.id,
          variantId: item.variantId,
          quantity: item.quantity,
          price: Number(item.sellingPrice?.toString().replace(/[^\d.]/g, '')) || 0
        }))
      };

      console.log('🛒 [CART] Creating order with payload:', payload);

      await createOrder(payload).unwrap();
      console.log('✅ [CART] Order created successfully');

      setAddressError('');
      console.log('🎉 [CART] Order placed, navigating to ThankYou');
      navigation.navigate('ThankYou');

    } catch (error: any) {
      console.error('❌ [CART] Order creation failed:', error);
      Alert.alert('Order Failed',`status code ${error?.status}  ${error?.data?.data}` || 'Failed to place order. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectAddress = (address: any) => {
    dispatch(setSelectedAddress(address.id));
    setAddressError('');
  };

  const handleAddNewAddress = () => {
    navigation.navigate('AddAddress', {
      returnScreen: 'Cart',
      onAddressAdded: () => {
        console.log('Address added, refreshing addresses...');
        refetchAddresses();
      }
    });
  };

  const handleEditAddress = (addressId: string) => {
    navigation.navigate('EditAddress', {
      addressId,
      returnScreen: 'Cart',
      onAddressUpdated: () => {
        console.log('Address updated, refreshing addresses...');
        refetchAddresses();
      }
    });
  };

  if (!currentUser?.id || isCartLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading your cart...</Text>
      </View>
    );
  }

  if (cartError) {
    console.error('Cart error:', cartError);
    if ('status' in cartError && (cartError.status === 404 || cartError.status === 400)) {
      const errorMessage = cartError.data?.message?.toLowerCase() || '';
      if (errorMessage.includes('cart not found') || errorMessage.includes('cart does not exist')) {
        return <EmptyCart fadeAnim={fadeAnim} />;
      }
    }
  }

  if (!displayItems || displayItems.length === 0) {
    return <EmptyCart fadeAnim={fadeAnim} />;
  }

  return (
    <View style={styles.container}>
      <CartHeader
        itemCount={displayItems.length}
        onClearCart={handleClearCart}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <Animated.View style={{ opacity: fadeAnim }}>
            <View style={styles.cartItemsContainer}>
              {displayItems.map((item, index) => (
                <CartItem
                  key={`${item.id}-${item.variant || 'default'}-${item.cartLineId || 'local'}`}
                  item={item}
                  onRemove={(id, variant, cartLineId) => handleRemoveItem(id, variant, cartLineId)}
                  onUpdateQuantity={(id, quantity, variant, cartLineId) =>
                    handleUpdateQuantity(id, quantity, variant, cartLineId)
                  }
                  isLast={index === displayItems.length - 1}
                  isUpdating={isUpdatingQuantity}
                />
              ))}
            </View>

            <AddressBook
              addresses={mappedAddresses}
              selectedAddress={selectedAddressId}
              onSelectAddress={handleSelectAddress}
              onAddNewAddress={handleAddNewAddress}
              onEditAddress={handleEditAddress}
              error={addressError}
              isLoading={isAddressesLoading}
            />

            <OrderSummary
              subtotal={cartTotals.subtotal}
              shippingCost={cartTotals.shippingCost}
              discount={cartTotals.discount}
              total={cartTotals.total}
              items={displayItems}
              onBuyNow={handleBuyNow}
              isLoading={isLoading || isOrderCreating}
              selectedAddress={selectedAddressId}
              addressError={addressError}
            />
            <View style={styles.bottomPadding} />
          </Animated.View>
        </ScrollView>
      )}
    </View>
  );
};

export default CartScreen;
