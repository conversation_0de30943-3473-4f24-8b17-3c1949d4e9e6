// import { createApi, BaseQueryFn } from '@reduxjs/toolkit/query/react';
// import { AxiosError } from 'axios';
// import AuthApiService from '../../../services/api/AuthApiService';

// interface CartLineItem {
//   productId: number;
//   variantId: number;
//   status: number;
//   quantity: number;
//   price: number;
// }

// interface UpdateCartLineItem {
//   cartId: number;
//   productId: number;
//   variantId: number;
//   status: number;
//   quantity: number;
// }

// interface UpdateCartRequest {
//   id: number;
//   userId: number;
//   status: number;
//   cartLine: UpdateCartLineItem[];
// }

// interface AddToCartRequest {
//   userId: number;
//   status: number;
//   cartLine: CartLineItem[];
// }

// interface AddToCartResponse {
//   data: {
//     id: number;
//     userId: number;
//     status: number;
//     createdBy: number;
//     updatedBy: number;
//     createdAt: number[];
//     updatedAt: number[];
//   };
// }

// type BaseQueryArgs = {
//   url: string;
//   method: 'GET' | 'POST' | 'PUT' | 'DELETE';
//   body?: any;
// };

// const customBaseQuery: BaseQueryFn<BaseQueryArgs, unknown, AxiosError> = async ({ url, method, body }) => {
//   try {
//     const result =
//       method === 'GET'
//         ? await AuthApiService.get(url)
//         : method === 'PUT'
//         ? await AuthApiService.put(url, body)
//         : method === 'DELETE'
//         ? await AuthApiService.delete(url)
//         : await AuthApiService.post(url, body);

//     return { data: result };
//   } catch (error: any) {
//     console.error('API error:', error.response || error);
//     return {
//       error: {
//         status: error.response?.status || 500,
//         data: error.response?.data || error.message,
//       },
//     };
//   }
// };

// export const cartApi = createApi({
//   reducerPath: 'cartApi',
//   baseQuery: customBaseQuery,
//   tagTypes: ['Cart', 'Order', 'Address'],
//   endpoints: (builder) => ({
//     // cart endpoints
//     addToCart: builder.mutation<AddToCartResponse, AddToCartRequest>({
//       query: (payload) => ({
//         url: 'api/v1/user/add-to-cart',
//         method: 'POST',
//         body: payload,
//       }),
//       transformResponse: (response: any) => response,
//       invalidatesTags: ['Cart'],
//     }),

//     // Updated updateCart mutation to match the corrected API specification
//     updateCart: builder.mutation<any, UpdateCartRequest>({
//       query: (payload) => ({
//         url: 'api/v1/user/cart/update',
//         method: 'PUT',
//         body: payload,
//       }),
//       transformResponse: (response: any) => {
//         console.log('🔄 [API] Update cart response:', response);
//         if (response?.status === "SUCCESS" && response?.data?.cart) {
//           return response.data.cart;
//         }
//         return response?.data || response;
//       },
//       transformErrorResponse: (response: any) => {
//         console.error('❌ [API] Update cart error:', response);
//         return response;
//       },
//       invalidatesTags: ['Cart'],
//     }),

//     // Alternative version - nested structure:
//     updateCartItemQuantity: builder.mutation<any, {
//       cartId: number;
//       userId: number;
//       productId: number;
//       variantId: number;
//       quantity: number;
//     }>({
//       query: ({ cartId, userId, productId, variantId, quantity }) => {
//         const payload = {
//           id: cartId,
//           userId: userId,
//           status: 1,
//           cartLine: [
//             {
//               cartId: cartId,
//               productId: productId,
//               variantId: variantId,
//               status: 1,
//               quantity: quantity,
//             }
//           ]
//         };
        
//         console.log('🔄 [API] Update quantity payload (nested):', payload);
        
//         return {
//           url: 'api/v1/user/cart/update',
//           method: 'PUT',
//           body: payload,
//         };
//       },
//       transformResponse: (response: any) => {
//         console.log('🔄 [API] Update quantity response:', response);
//         if (response?.status === "SUCCESS" && response?.data?.cart) {
//           return response.data.cart;
//         }
//         return response?.data || response;
//       },
//       invalidatesTags: ['Cart'],
//     }),



//     getCartByUserId: builder.query<any, number>({
//     query: (userId) => ({
//       url: `api/v1/user/user-cart/${userId}`,
//       method: 'GET',
//     }),
//     transformResponse: (response: any) => {
//       console.log('🔄 [API] Raw cart response:', response);
      
//       if (response?.message === "Cart fetched successfully" && response?.data){
//         return response.data;
//       }
      
//       // Handle the case where we have data but also an error
//       if (response?.data && response?.data?.cartLine) {
//         console.log('🔄 [API] Found cart data despite error, using it');
//         return response.data;
//       }
      
//       return null;
//     },
//     transformErrorResponse: (response: any, meta: any, arg: any) => {
//       console.log('🔄 [API] Cart error response:', response);
//       console.log('🔄 [API] Cart error meta:', meta);
      
//       // Check if the error response actually contains valid data
//       if (meta?.response?.data?.data?.cartLine) {
//         console.log('🔄 [API] Error response contains valid cart data');
//         // Return the data instead of error
//         return { 
//           isActuallySuccess: true, 
//           data: meta.response.data.data 
//         };
//       }
      
//       return response;
//     },
//     keepUnusedDataFor: 0,
//     providesTags: ['Cart'],
//   }),

    
//     deleteCartItem: builder.mutation<any, { cartId: number; productId: number; variantId: number }>({
//       query: ({ cartId, productId, variantId }) => ({
//         url: `api/v1/user/cart/${cartId}/${productId}/${variantId}`,
//         method: 'DELETE',
//       }),
//       transformResponse: (response: any) => response?.data || [],
//       transformErrorResponse: (response: any) => {
//         // If it's a "cart not found" error, treat it as success
//         if ((response?.status === 400 || response?.status === 404) && 
//             response?.data?.message?.toLowerCase().includes('cart not found')) {
//           return { isCartDeleted: true, originalError: response };
//         }
//         return response;
//       },
//       invalidatesTags: ['Cart'],
//     }),


//     createOrder: builder.mutation<any, any>({
//       query: (payload) => ({
//         url: 'api/v1/order/create-order',
//         method: 'POST',
//         body: payload,
//       }),
//       transformResponse: (response: any) => response?.data || [],
//       invalidatesTags: ['Cart', 'Order'],
//     }),

//     createConfirmOrder: builder.mutation<any, any>({
//       query: (payload) => ({
//         url: 'api/v1/order/confirm-order',
//         method: 'POST',
//         body: payload,
//       }),
//       transformResponse: (response: any) => response,
//       invalidatesTags: ['Cart', 'Order'],
//     }),

//     getAllOrdersByUserId: builder.query<any[], number | undefined>({
//       query: (userId) => ({
//         url: `api/v1/order/user/orders?userId=${userId}`,
//         method: 'GET',
//       }),
//       transformResponse: (response: any) => {
//         console.log('getAllOrdersByUserId raw response:', response);
//         if (Array.isArray(response)) return response;
//         if (Array.isArray(response?.data)) return response.data;
//         return [];
//       },
//       providesTags: ['Order'],
//     }),

//     getAllOrders: builder.query<any[], void>({
//       query: () => ({
//         url: 'api/v1/order/all',
//         method: 'GET',
//       }),
//       transformResponse: (response: any) => {
//         console.log('getAllOrders raw response:', response);
//         // Handle the actual API response structure
//         if (response?.message === 'SUCCESS' && Array.isArray(response?.data)) {
//           return response.data;
//         }
//         if (Array.isArray(response)) {
//           return response;
//         }
//         return [];
//       },
//       providesTags: ['Order'],
//     }),

//     getConfirmOrderIdByOrderId: builder.query<any, number>({
//       query: (orderId) => ({
//         url: `api/v1/order/get-confirm-order/${orderId}`,
//         method: 'GET',
//       }),
//       transformResponse: (response: any) => response?.data || [],
//       providesTags: ['Order'],
//     }),

//     // address endpoints

//     createAddress: builder.mutation<any, any>({
//       query: (payload) => ({
//         url: 'api/v1/user/address',
//         method: 'POST',
//         body: payload,
//       }),
//       transformResponse: (response: any) => response?.data || [],
//       invalidatesTags: ['Address'],
//     }),

//     getAddressesByUserId: builder.query<any, number>({
//       query: (userId) => ({
//         url: `api/v1/user/addresses/${userId}`,
//         method: 'GET',
//       }),
//       transformResponse: (response: any) => response?.data || [],
//       providesTags: ['Address'],
//     }),

//     getAddressByAddressId: builder.query<any, number>({
//       query: (addressId) => ({
//         url: `api/v1/user/address/${addressId}`,
//         method: 'GET',
//       }),
//       transformResponse: (response: any) => response?.data || [],
//       providesTags: ['Address'],
//     }),

//     updateAddressByAddressId: builder.mutation<any, { addressId: number; [key: string]: any }>({
//       query: ({ addressId, ...payload }) => ({
//         url: `api/v1/user/address/${addressId}`,
//         method: 'PUT',
//         body: payload,
//       }),
//       transformResponse: (response: any) => response?.data || [],
//       invalidatesTags: ['Address'],
//     }),
//   }),
// });

// // Export types
// export type { CartLineItem, AddToCartRequest, AddToCartResponse };

// // Export hooks
// export const {
//   useAddToCartMutation,
//   useUpdateCartMutation,
//   useUpdateCartItemQuantityMutation,
//   useGetCartByUserIdQuery,
//   useDeleteCartItemMutation,
//   useCreateOrderMutation,
//   useCreateConfirmOrderMutation,
//   useGetAllOrdersByUserIdQuery,
//   useGetAllOrdersQuery,
//   useGetConfirmOrderIdByOrderIdQuery,
//   useCreateAddressMutation,
//   useGetAddressesByUserIdQuery,
//   useGetAddressByAddressIdQuery,
//   useUpdateAddressByAddressIdMutation,
// } = cartApi;



import { createApi, BaseQueryFn } from '@reduxjs/toolkit/query/react';
import { AxiosError } from 'axios';
import AuthApiService from '../../../services/api/AuthApiService';

interface CartLineItem {
  productId: number;
  variantId: number;
  status: number;
  quantity: number;
  price: number;
}

interface UpdateCartLineItem {
  cartId: number;
  productId: number;
  variantId: number;
  status: number;
  quantity: number;
}

interface UpdateCartRequest {
  id: number;
  userId: number;
  status: number;
  cartLine: UpdateCartLineItem[];
}

interface AddToCartRequest {
  userId: number;
  status: number;
  cartLine: CartLineItem[];
}

interface AddToCartResponse {
  data: {
    id: number;
    userId: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createdAt: number[];
    updatedAt: number[];
  };
}

type BaseQueryArgs = {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: any;
};

const customBaseQuery: BaseQueryFn<BaseQueryArgs, unknown, AxiosError> = async ({ url, method, body }) => {
  try {
    const result =
      method === 'GET'
        ? await AuthApiService.get(url)
        : method === 'PUT'
        ? await AuthApiService.put(url, body)
        : method === 'DELETE'
        ? await AuthApiService.delete(url)
        : await AuthApiService.post(url, body);

    return { data: result };
  } catch (error: any) {
    // console.error('API error:', error.response || error);
    return {
      error: {
        status: error.response?.status || 500,
        data: error.response?.data || error.message,
      },
    };
  }
};

export const cartApi = createApi({
  reducerPath: 'cartApi',
  baseQuery: customBaseQuery,
  tagTypes: ['Cart', 'Order', 'Address'],
  endpoints: (builder) => ({
    // cart endpoints
    addToCart: builder.mutation<AddToCartResponse, AddToCartRequest>({
      query: (payload) => ({
        url: 'api/v1/user/add-to-cart',
        method: 'POST',
        body: payload,
      }),
      transformResponse: (response: any) => response,
      invalidatesTags: ['Cart'],
    }),

    updateCart: builder.mutation<any, UpdateCartRequest>({
      query: (payload) => ({
        url: 'api/v1/user/cart/update',
        method: 'PUT',
        body: payload,
      }),
      transformResponse: (response: any) => {
        console.log('🔄 [API] Update cart response:', response);
        if (response?.status === "SUCCESS" && response?.data?.cart) {
          return response.data.cart;
        }
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.error('❌ [API] Update cart error:', response);
        return response;
      },
      invalidatesTags: ['Cart'],
    }),

    updateCartItemQuantity: builder.mutation<any, {
      cartId: number;
      userId: number;
      productId: number;
      variantId: number;
      quantity: number;
    }>({
      query: ({ cartId, userId, productId, variantId, quantity }) => {
        const payload = {
          id: cartId,
          userId: userId,
          status: 1,
          cartLine: [
            {
              cartId: cartId,
              productId: productId,
              variantId: variantId,
              status: 1,
              quantity: quantity,
            }
          ]
        };
        
        console.log('🔄 [API] Update quantity payload:', payload);
        
        return {
          url: 'api/v1/user/cart/update',
          method: 'PUT',
          body: payload,
        };
      },
      transformResponse: (response: any) => {
        console.log('🔄 [API] Update quantity response:', response);
        if (response?.status === "SUCCESS" && response?.data?.cart) {
          return response.data.cart;
        }
        return response?.data || response;
      },
      transformErrorResponse: (response: any) => {
        console.error('❌ [API] Update quantity error:', response);
        return response;
      },
      invalidatesTags: ['Cart'],
    }),

    getCartByUserId: builder.query<any, number>({
      query: (userId) => ({
        url: `api/v1/user/user-cart/${userId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('🔄 [API] Raw cart response:', response);
        
        if (response?.message === "Cart fetched successfully" && response?.data){
          return response.data;
        }
        
        if (response?.data && response?.data?.cartLine) {
          console.log('🔄 [API] Found cart data despite error, using it');
          return response.data;
        }
        
        return null;
      },
      transformErrorResponse: (response: any, meta: any, arg: any) => {
        console.log('🔄 [API] Cart error response:', response);
        console.log('🔄 [API] Cart error meta:', meta);
        
        if (meta?.response?.data?.data?.cartLine) {
          console.log('🔄 [API] Error response contains valid cart data');
          return { 
            isActuallySuccess: true, 
            data: meta.response.data.data 
          };
        }
        
        return response;
      },
      keepUnusedDataFor: 0,
      providesTags: ['Cart'],
    }),

    deleteCartItem: builder.mutation<any, { cartId: number; productId: number; variantId: number }>({
      query: ({ cartId, productId, variantId }) => ({
        url: `api/v1/user/cart/${cartId}/${productId}/${variantId}`,
        method: 'DELETE',
      }),
      transformResponse: (response: any) => response?.data || [],
      transformErrorResponse: (response: any) => {
        if ((response?.status === 400 || response?.status === 404) && 
            response?.data?.message?.toLowerCase().includes('cart not found')) {
          return { isCartDeleted: true, originalError: response };
        }
        return response;
      },
      invalidatesTags: ['Cart'],
    }),

    createOrder: builder.mutation<any, any>({
      query: (payload) => ({
        url: 'api/v1/order/create-order',
        method: 'POST',
        body: payload,
      }),
      transformResponse: (response: any) => response?.data || [],
      invalidatesTags: ['Cart', 'Order'],
    }),

    createConfirmOrder: builder.mutation<any, any>({
      query: (payload) => ({
        url: 'api/v1/order/confirm-order',
        method: 'POST',
        body: payload,
      }),
      transformResponse: (response: any) => response,
      invalidatesTags: ['Cart', 'Order'],
    }),

    getAllOrdersByUserId: builder.query<any[], number | undefined>({
      query: (userId) => ({
        url: `api/v1/order/user/orders?userId=${userId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('getAllOrdersByUserId raw response:', response);
        if (Array.isArray(response)) return response;
        if (Array.isArray(response?.data)) return response.data;
        return [];
      },
      providesTags: ['Order'],
    }),

    getAllOrders: builder.query<any[], void>({
      query: () => ({
        url: 'api/v1/order/all',
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        console.log('getAllOrders raw response:', response);
        if (response?.message === 'SUCCESS' && Array.isArray(response?.data)) {
          return response.data;
        }
        if (Array.isArray(response)) {
          return response;
        }
        return [];
      },
      providesTags: ['Order'],
    }),

    getConfirmOrderIdByOrderId: builder.query<any, number>({
      query: (orderId) => ({
        url: `api/v1/order/get-confirm-order/${orderId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => response?.data || [],
      providesTags: ['Order'],
    }),

    // address endpoints
    createAddress: builder.mutation<any, any>({
      query: (payload) => ({
        url: 'api/v1/user/address',
        method: 'POST',
        body: payload,
      }),
      transformResponse: (response: any) => response?.data || [],
      invalidatesTags: ['Address'],
    }),

    getAddressesByUserId: builder.query<any, number>({
      query: (userId) => ({
        url: `api/v1/user/addresses/${userId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => response?.data || [],
      providesTags: ['Address'],
    }),

    getAddressByAddressId: builder.query<any, number>({
      query: (addressId) => ({
        url: `api/v1/user/address/${addressId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => response?.data || [],
      providesTags: ['Address'],
    }),

    updateAddressByAddressId: builder.mutation<any, { addressId: number; [key: string]: any }>({
      query: ({ addressId, ...payload }) => ({
        url: `api/v1/user/address/${addressId}`,
        method: 'PUT',
        body: payload,
      }),
      transformResponse: (response: any) => response?.data || [],
      invalidatesTags: ['Address'],
    }),
  }),
});

export type { CartLineItem, AddToCartRequest, AddToCartResponse };

export const {
  useAddToCartMutation,
  useUpdateCartMutation,
  useUpdateCartItemQuantityMutation,
  useGetCartByUserIdQuery,
  useDeleteCartItemMutation,
  useCreateOrderMutation,
  useCreateConfirmOrderMutation,
  useGetAllOrdersByUserIdQuery,
  useGetAllOrdersQuery,
  useGetConfirmOrderIdByOrderIdQuery,
  useCreateAddressMutation,
  useGetAddressesByUserIdQuery,
  useGetAddressByAddressIdQuery,
  useUpdateAddressByAddressIdMutation,
} = cartApi;
