// import React from 'react';
// import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
// import Icon from 'react-native-vector-icons/MaterialIcons';

// interface CartItemProps {
//   item: {
//     id: number;
//     name: string;
//     price: string;
//     sellingPrice: string;
//     image: string;
//     quantity: number;
//     variant?: string;
//     cartLineId?: number;
//     variantId?: number;
//   };
//   onRemove: (id: number, variant?: string, cartLineId?: number) => void;
//   onUpdateQuantity: (id: number, quantity: number, variant?: string, cartLineId?: number) => void;
//   isLast?: boolean;
// }

// const CartItem = ({ item, onRemove, onUpdateQuantity, isLast }: CartItemProps) => {
//   console.log('Rendering CartItem:', item);
//   return (
//     <View style={[styles.cartItem, isLast && styles.lastCartItem]}>
//       <View style={styles.cartItemRow}>
//         <Image
//           source={{ uri: item.image }}
//           style={styles.productImage}
//           resizeMode="contain"
//         />

//         <View style={styles.productDetails}>
//           <View style={styles.productHeader}>
//             <View style={styles.productInfo}>
//               <Text style={styles.productName} numberOfLines={2}>
//                 {item.name}
//               </Text>
//               {item.variant && (
//                 <View style={styles.variantContainer}>
//                   <View style={[styles.variantDot, { backgroundColor: item.variant.toLowerCase() }]} />
//                   <Text style={styles.variantText}>{item.variant}</Text>
//                 </View>
//               )}
//             </View>
//             <TouchableOpacity
//               onPress={() => onRemove(item.id, item.variant, item.cartLineId)}
//               style={styles.removeButton}
//               hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
//             >
//               <Icon name="close" size={20} color="#6b7280" />
//             </TouchableOpacity>
//           </View>

//           <View style={styles.priceQuantityRow}>
//             <Text style={styles.productPrice}>{item.sellingPrice}</Text>
//             <View style={styles.quantityControls}>
//               <TouchableOpacity
//                 onPress={() => {
//                   if (item.quantity > 1) {
//                     onUpdateQuantity(item.id, item.quantity - 1, item.variant, item.cartLineId);
//                   }
//                 }}
//                 style={[
//                   styles.quantityButton,
//                   item.quantity <= 1 && styles.quantityButtonDisabled
//                 ]}
//                 disabled={item.quantity <= 1}
//               >
//                 <Icon
//                   name="remove"
//                   size={18}
//                   color={item.quantity <= 1 ? "#d1d5db" : "#4b5563"}
//                 />
//               </TouchableOpacity>
//               <Text style={styles.quantityText}>{item.quantity}</Text>
//               <TouchableOpacity
//                 onPress={() => onUpdateQuantity(item.id, item.quantity + 1, item.variant, item.cartLineId)}
//                 style={styles.quantityButton}
//               >
//                 <Icon name="add" size={18} color="#4b5563" />
//               </TouchableOpacity>
//             </View>
//           </View>
//         </View>
//       </View>
//     </View>
//   );
// };

// // ... styles remain the same
// const styles = StyleSheet.create({
//   cartItem: {
//     backgroundColor: 'white',
//     borderRadius: 12,
//     padding: 14,
//     marginBottom: 10,
//     elevation: 1,
//     shadowColor: '#000',
//     shadowOffset: { width: 0, height: 1 },
//     shadowOpacity: 0.05,
//     shadowRadius: 2,
//   },
//   lastCartItem: {
//     marginBottom: 0,
//   },
//   cartItemRow: {
//     flexDirection: 'row',
//   },
//   productImage: {
//     width: 90,
//     height: 90,
//     borderRadius: 8,
//     backgroundColor: '#f9fafb',
//   },
//   productDetails: {
//     flex: 1,
//     marginLeft: 14,
//   },
//   productHeader: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//   },
//   productInfo: {
//     flex: 1,
//     paddingRight: 10,
//   },
//   productName: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: '#1f2937',
//     lineHeight: 22,
//   },
//   variantContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginTop: 4,
//   },
//   variantDot: {
//     width: 10,
//     height: 10,
//     borderRadius: 5,
//     marginRight: 6,
//     borderWidth: 1,
//     borderColor: '#e5e7eb',
//   },
//   variantText: {
//     fontSize: 14,
//     color: '#6b7280',
//   },
//   removeButton: {
//     width: 28,
//     height: 28,
//     padding: 4,
//     backgroundColor: '#f3f4f6',
//     borderRadius: 14,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   priceQuantityRow: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     marginTop: 12,
//   },
//   productPrice: {
//     fontSize: 16,
//     fontWeight: '700',
//     color: '#6366f1',
//   },
//   quantityControls: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: '#f3f4f6',
//     borderRadius: 20,
//     paddingHorizontal: 4,
//   },
//   quantityButton: {
//     width: 30,
//     height: 30,
//     alignItems: 'center',
//     justifyContent: 'center',
//     borderRadius: 15,
//   },
//   quantityButtonDisabled: {
//     opacity: 0.5,
//   },
//   quantityText: {
//     fontSize: 15,
//     fontWeight: '600',
//     color: '#374151',
//     width: 30,
//     textAlign: 'center',
//   },
//   itemTotalContainer: {
//     flexDirection: 'row',
//     justifyContent: 'flex-end',
//     alignItems: 'center',
//     marginTop: 10,
//   },
//   itemTotalLabel: {
//     fontSize: 14,
//     color: '#6b7280',
//   },
//   itemTotalValue: {
//     fontSize: 15,
//     fontWeight: '600',
//     color: '#374151',
//     marginLeft: 6,
//   },
// });

// export default CartItem;

import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface CartItemProps {
  item: {
    id: number;
    name: string;
    price: string;
    sellingPrice: string;
    image: string;
    quantity: number;
    variant?: string;
    cartLineId?: number;
    variantId?: number;
  };
  onRemove: (id: number, variant?: string, cartLineId?: number) => void;
  onUpdateQuantity: (id: number, quantity: number, variant?: string, cartLineId?: number) => void;
  isLast?: boolean;
  isUpdating?: boolean;
}

const CartItem = ({ item, onRemove, onUpdateQuantity, isLast, isUpdating }: CartItemProps) => {
  const [localQuantity, setLocalQuantity] = useState(item.quantity);
  const [isLocalUpdating, setIsLocalUpdating] = useState(false);

  // Update local quantity when item quantity changes from API
  useEffect(() => {
    if (!isLocalUpdating) {
      setLocalQuantity(item.quantity);
    }
  }, [item.quantity, isLocalUpdating]);

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1 || isUpdating || isLocalUpdating) return;
    
    console.log(`🔄 [CART_ITEM] Changing quantity from ${localQuantity} to ${newQuantity}`);
    
    // Set local updating state
    setIsLocalUpdating(true);
    setLocalQuantity(newQuantity);
    
    try {
      await onUpdateQuantity(item.id, newQuantity, item.variant, item.cartLineId);
    } catch (error) {
      // Revert on error
      setLocalQuantity(item.quantity);
      console.error('Failed to update quantity, reverting:', error);
    } finally {
      setIsLocalUpdating(false);
    }
  };

  const handleIncrement = () => {
    const newQuantity = localQuantity + 1;
    handleQuantityChange(newQuantity);
  };

  const handleDecrement = () => {
    if (localQuantity <= 1) return;
    const newQuantity = localQuantity - 1;
    handleQuantityChange(newQuantity);
  };

  const displayQuantity = isLocalUpdating ? localQuantity : item.quantity;

  return (
    <View style={[styles.cartItem, isLast && styles.lastCartItem]}>
      <View style={styles.cartItemRow}>
        <Image
          source={{ uri: item.image }}
          style={styles.productImage}
          resizeMode="contain"
        />

        <View style={styles.productDetails}>
          <View style={styles.productHeader}>
            <View style={styles.productInfo}>
              <Text style={styles.productName} numberOfLines={2}>
                {item.name}
              </Text>
              {item.variant && (
                <View style={styles.variantContainer}>
                  <View style={[styles.variantDot, { backgroundColor: item.variant.toLowerCase() }]} />
                  <Text style={styles.variantText}>{item.variant}</Text>
                </View>
              )}
            </View>
            <TouchableOpacity
              onPress={() => onRemove(item.id, item.variant, item.cartLineId)}
              style={styles.removeButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              disabled={isUpdating || isLocalUpdating}
            >
              <Icon name="close" size={20} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.priceQuantityRow}>
            <Text style={styles.productPrice}>₹{item.sellingPrice}</Text>
            <View style={styles.quantityControls}>
              <TouchableOpacity
                onPress={handleDecrement}
                style={[
                  styles.quantityButton,
                  (displayQuantity <= 1 || isUpdating || isLocalUpdating) && styles.quantityButtonDisabled
                ]}
                disabled={displayQuantity <= 1 || isUpdating || isLocalUpdating}
              >
                <Icon
                  name="remove"
                  size={18}
                  color={displayQuantity <= 1 ? "#d1d5db" : "#4b5563"}
                />
              </TouchableOpacity>
              
              <View style={styles.quantityTextContainer}>
                {(isUpdating || isLocalUpdating) ? (
                  <ActivityIndicator size={16} color="#374151" />
                ) : (
                  <Text style={styles.quantityText}>{displayQuantity}</Text>
                )}
              </View>
              
              <TouchableOpacity
                onPress={handleIncrement}
                style={[
                  styles.quantityButton,
                  (isUpdating || isLocalUpdating) && styles.quantityButtonDisabled
                ]}
                disabled={isUpdating || isLocalUpdating}
              >
                <Icon name="add" size={18} color="#4b5563" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cartItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 14,
    marginBottom: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  lastCartItem: {
    marginBottom: 0,
  },
  cartItemRow: {
    flexDirection: 'row',
  },
  productImage: {
    width: 90,
    height: 90,
    borderRadius: 8,
    backgroundColor: '#f9fafb',
  },
  productDetails: {
    flex: 1,
    marginLeft: 14,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  productInfo: {
    flex: 1,
    paddingRight: 10,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    lineHeight: 22,
  },
  variantContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  variantDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 6,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  variantText: {
    fontSize: 14,
    color: '#6b7280',
  },
  removeButton: {
    width: 28,
    height: 28,
    padding: 4,
    backgroundColor: '#f3f4f6',
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  priceQuantityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6366f1',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 20,
    paddingHorizontal: 4,
  },
  quantityButton: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 15,
  },
  quantityButtonDisabled: {
    opacity: 0.5,
  },
  quantityTextContainer: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
  },
});

export default CartItem;
