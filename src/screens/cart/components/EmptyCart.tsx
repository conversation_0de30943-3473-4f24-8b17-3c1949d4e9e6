import React from 'react';
import { View, Text, TouchableOpacity, Animated, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

interface EmptyCartProps {
  fadeAnim: Animated.Value;
}

const EmptyCart = ({ fadeAnim }: EmptyCartProps) => {
  const navigation = useNavigation<any>();

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        <Icon name="shopping-outline" size={90} color="#9ca3af" />
        <Text style={styles.title}>Your cart is empty</Text>
        <Text style={styles.subtitle}>
          Looks like you haven't added any items to your cart yet
        </Text>
        <TouchableOpacity
          onPress={() => navigation.navigate('Home')}
          style={styles.button}
          activeOpacity={0.8}
        >
          <Icon name="shopping-outline" size={24} color="white" />
          <Text style={styles.buttonText}>Continue Shopping</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  title: {
    fontSize: 22,
    fontWeight: '600',
    color: '#374151',
    marginTop: 20,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 22,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6366f1',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 30,
    marginTop: 30,
    elevation: 3,
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 10,
  },
});

export default EmptyCart;