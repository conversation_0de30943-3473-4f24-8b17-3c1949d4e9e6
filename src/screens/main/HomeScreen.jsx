import { View, ScrollView, TouchableOpacity, StatusBar, RefreshControl, Text, ActivityIndicator } from "react-native";
import React, { useState, useEffect } from "react";
import Icon from 'react-native-vector-icons/MaterialIcons';
import SearchBar from "../../components/Home/SearchBar";
import CategoryTabs from "../../components/Home/CategoryTabs";
import Banner from "../../components/Home/Banner";
import PromotionalScroll from "../../components/Home/PromotionalScroll";
import ProductCard from "../../components/common/ProductCard";
import Header from '../../components/Home/Header';
import { useUser } from '../../context/UserContext';
import ProductService from '../../services/ProductService';

export default function HomeScreen({ navigation }) {
  const { currentUser } = useUser();
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [trendingProducts, setTrendingProducts] = useState([]);

  // Load products on mount
  useEffect(() => {
    loadProducts();
  }, [currentUser]);

  // Load products from service
  const loadProducts = async () => {
    try {
      setLoading(true);
      const products = await ProductService.getProducts(
        currentUser?.role || 'PUBLIC'
      );
      setTrendingProducts(products.slice(0, 3)); // Just show first 3 products
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadProducts().then(() => setRefreshing(false));
  }, [currentUser]);

  const handleExploreAll = () => {
    navigation.navigate('ProductListing', { category: 'All Products', categoryId: 0 });
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Main Content */}
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Banner Section */}
        <View className="mt-3">
          <Banner />
        </View>

        {/* Categories Section */}
        <View className="mt-6">
          <Text className="text-xl font-bold text-gray-800 px-4 mb-3">
            Shop by Category
          </Text>
          <CategoryTabs />
        </View>

        {/* Promotions Section */}
        <View className="mt-6">
          <PromotionalScroll />
        </View>

        {/* Featured Products Section */}
        <View className="mt-8 mb-6">
          <View className="flex-row justify-between items-center px-4 mb-3">
            <Text className="text-xl font-bold text-gray-800">
              Trending Products
            </Text>
            <TouchableOpacity
              onPress={handleExploreAll}
              activeOpacity={0.7}
            >
              <Text className="text-indigo-600 font-semibold">
                View All
              </Text>
            </TouchableOpacity>
          </View>

          {loading ? (
            <View className="py-8 items-center justify-center">
              <ActivityIndicator size="large" color="#6366f1" />
              <Text className="mt-2 text-gray-500">Loading products...</Text>
            </View>
          ) : (
            <View>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="py-2"
                contentContainerStyle={{ paddingHorizontal: 16, gap: 12 }}
              >
                {trendingProducts.map(product => (
                  <ProductCard
                    key={product.id}
                    productId={product.id}
                    productName={product.name}
                    description={product.description}
                    batteryLife={product.specifications?.batteryLife || ''}
                    standbyTime={product.specifications?.standbyTime || ''}
                    price={product.showPrice ? ProductService.formatPrice(product.calculatedPrice) : 'Login to see price'}
                    moq={product.moq || '1'}
                    imageUrl={product.images[0]}
                    category={product.category}
                    onPress={() => navigation.navigate('ProductDetail', { product: { id: product.id } })}
                  />
                ))}
              {/* Explore All Card */}
              <TouchableOpacity
                onPress={handleExploreAll}
                className="mx-auto mt-3 bg-white rounded-lg overflow-hidden shadow-sm"
                style={{ height: 300, width: 180 }}
                activeOpacity={0.7}
              >
                <View className="h-[60%] bg-indigo-50 items-center justify-center">
                  <View className="bg-white p-4 rounded-full shadow-sm">
                    <Icon name="explore" size={32} color="#6366f1" />
                  </View>
                </View>

                <View className="p-3 items-center justify-center flex-1">
                  <Text className="text-base font-bold text-indigo-600">
                    Explore All
                  </Text>
                  <Text className="text-sm text-gray-500 mt-1 text-center">
                    View Complete{'\n'}Product Catalog
                  </Text>
                </View>
              </TouchableOpacity>
              </ScrollView>

            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}
