import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, Image, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { UserRole } from '../../data/mockData';
import { useUser } from '../../context/UserContext';
import DataService from '../../services/DataService';

type LeaderboardTab = 'superStockists' | 'distributors' | 'retailers' | 'progress' | 'monthly' | 'category' | 'incentives';

interface LeaderboardEntry {
  userId: string;
  name: string;
  role: UserRole;
  avatar?: string;
  salesAmount: number;
  salesCount: number;
  rank: number;
  target?: number;
  achievement?: number;
  monthlyPerformance?: {
    month: string;
    sales: number;
    orders: number;
  }[];
  categoryPerformance?: {
    category: string;
    sales: number;
    percentage: number;
  }[];
  incentives?: {
    name: string;
    amount: number;
    status: string;
  }[];
}

const LeaderboardScreen: React.FC = () => {
  const { currentUser } = useUser();
  const [activeTab, setActiveTab] = useState<LeaderboardTab>('superStockists');
  const [filteredData, setFilteredData] = useState<LeaderboardEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Determine which tabs to show based on user role
  const getAvailableTabs = (): LeaderboardTab[] => {
    if (!currentUser) return [];

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return ['superStockists', 'distributors', 'retailers'];
      case UserRole.SUPER_STOCKIST:
        return ['distributors'];
      case UserRole.DISTRIBUTOR:
        return ['retailers'];
      case UserRole.RETAILER:
        return ['progress', 'monthly', 'category', 'incentives'];
      default:
        return [];
    }
  };

  // Set initial active tab based on user role
  useEffect(() => {
    const availableTabs = getAvailableTabs();
    if (availableTabs.length > 0 && !availableTabs.includes(activeTab)) {
      setActiveTab(availableTabs[0]);
    }
  }, [currentUser, activeTab]);

  // Filter leaderboard data based on user role and relationships
  useEffect(() => {
    loadLeaderboardData();
  }, [currentUser, activeTab]);

  const loadLeaderboardData = async () => {
    try {
      setIsLoading(true);

      if (!currentUser) {
        setFilteredData([]);
        setIsLoading(false);
        return;
      }

      // Get leaderboard data from service
      const leaderboardData = await DataService.getLeaderboardData(
        currentUser.role,
        currentUser.id
      );

      if (!leaderboardData) {
        setFilteredData([]);
        setIsLoading(false);
        return;
      }

      let data: LeaderboardEntry[] = [];

      // Extract data based on active tab
      switch (activeTab) {
        case 'superStockists':
          data = leaderboardData.superStockists || [];
          break;
        case 'distributors':
          data = leaderboardData.distributors || [];
          break;
        case 'retailers':
          data = leaderboardData.retailers || [];
          break;
        case 'progress':
        case 'monthly':
        case 'category':
        case 'incentives':
          // For retailer tabs, we use the retailer's own data
          if (leaderboardData.retailers && leaderboardData.retailers.length > 0) {
            data = leaderboardData.retailers;
          }
          break;
      }

      setFilteredData(data);
    } catch (error) {
      console.error('Error loading leaderboard data:', error);
      setFilteredData([]);
    } finally {
      setIsLoading(false);
    }
  };

  const renderItem = ({ item }: { item: LeaderboardEntry }) => (
    <View style={styles.itemContainer}>
      <View style={styles.rankContainer}>
        <Text style={styles.rankText}>{item.rank}</Text>
      </View>
      <View style={styles.avatarContainer}>
        {item.avatar ? (
          <Image source={{ uri: item.avatar }} style={styles.avatar} />
        ) : (
          <View style={[styles.avatar, styles.placeholderAvatar]}>
            <Text style={styles.placeholderText}>{item.name.charAt(0)}</Text>
          </View>
        )}
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.nameText}>{item.name}</Text>
        <Text style={styles.roleText}>
          {item.role === UserRole.SUPER_STOCKIST ? 'Super Stockist' :
           item.role === UserRole.DISTRIBUTOR ? 'Distributor' : 'Retailer'}
        </Text>
      </View>
      <View style={styles.statsContainer}>
        <Text style={styles.amountText}>₹{item.salesAmount.toLocaleString()}</Text>
        <Text style={styles.countText}>{item.salesCount} orders</Text>
      </View>
    </View>
  );

  // Get tab label
  const getTabLabel = (tab: LeaderboardTab): string => {
    switch (tab) {
      case 'superStockists': return 'Super Stockists';
      case 'distributors': return 'Distributors';
      case 'retailers': return 'Retailers';
      case 'progress': return 'Overview';
      case 'monthly': return 'Monthly';
      case 'category': return 'Categories';
      case 'incentives': return 'Incentives';
      default: return '';
    }
  };

  const renderHeader = () => {
    const availableTabs = getAvailableTabs();

    return (
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>Leaderboard</Text>
        {activeTab === 'progress' ? (
          <Text style={styles.headerSubtitle}>Your sales performance</Text>
        ) : (
          <Text style={styles.headerSubtitle}>Top performers this month</Text>
        )}

        {availableTabs.length > 1 && (
          <View style={styles.tabContainer}>
            {availableTabs.map(tab => (
              <TouchableOpacity
                key={tab}
                style={[styles.tab, activeTab === tab && styles.activeTab]}
                onPress={() => setActiveTab(tab)}
              >
                <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                  {getTabLabel(tab)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Icon name="emoji-events" size={64} color="#e5e7eb" />
      <Text style={styles.emptyText}>No data available</Text>
    </View>
  );

  // Render retailer progress overview
  const renderRetailerProgress = () => {
    if (filteredData.length === 0) return renderEmptyList();

    const retailer = filteredData[0];
    if (!retailer) return renderEmptyList();

    return (
      <View style={styles.progressContainer}>
        {/* Sales Performance Card */}
        <View style={styles.progressCard}>
          <Text style={styles.progressTitle}>Your Sales Performance</Text>

          <View style={styles.progressRow}>
            <View style={styles.progressItem}>
              <Text style={styles.progressValue}>₹{retailer.salesAmount.toLocaleString()}</Text>
              <Text style={styles.progressLabel}>Total Sales</Text>
            </View>

            <View style={styles.progressDivider} />

            <View style={styles.progressItem}>
              <Text style={styles.progressValue}>{retailer.salesCount}</Text>
              <Text style={styles.progressLabel}>Orders</Text>
            </View>
          </View>

          <View style={styles.rankIndicator}>
            <Text style={styles.rankIndicatorText}>Your Rank: #{retailer.rank}</Text>
          </View>
        </View>

        {/* Target Achievement Card */}
        {retailer.target && (
          <View style={[styles.progressCard, { marginTop: 16 }]}>
            <Text style={styles.progressTitle}>Target Achievement</Text>

            <View style={styles.targetContainer}>
              <View style={styles.targetProgressBar}>
                <View
                  style={[styles.targetProgress, { width: `${Math.min(retailer.achievement || 0, 100)}%` }]}
                />
              </View>

              <View style={styles.targetDetails}>
                <View style={styles.targetRow}>
                  <Text style={styles.targetLabel}>Target:</Text>
                  <Text style={styles.targetValue}>₹{retailer.target.toLocaleString()}</Text>
                </View>

                <View style={styles.targetRow}>
                  <Text style={styles.targetLabel}>Achieved:</Text>
                  <Text style={styles.targetValue}>₹{retailer.salesAmount.toLocaleString()}</Text>
                </View>

                <View style={styles.targetRow}>
                  <Text style={styles.targetLabel}>Remaining:</Text>
                  <Text style={styles.targetValue}>₹{(retailer.target - retailer.salesAmount).toLocaleString()}</Text>
                </View>

                <View style={styles.targetRow}>
                  <Text style={styles.targetLabel}>Achievement:</Text>
                  <Text style={styles.targetValue}>{retailer.achievement}%</Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  // Render monthly performance
  const renderMonthlyPerformance = () => {
    if (filteredData.length === 0 || !filteredData[0]?.monthlyPerformance) return renderEmptyList();

    const retailer = filteredData[0];
    if (!retailer) return renderEmptyList();
    const monthlyData = retailer.monthlyPerformance || [];

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressCard}>
          <Text style={styles.progressTitle}>Monthly Performance</Text>

          {monthlyData.map((item: { month: string; sales: number; orders: number }, index: number) => (
            <View key={index} style={styles.monthlyItem}>
              <View style={styles.monthlyHeader}>
                <Text style={styles.monthlyMonth}>{item.month}</Text>
                <Text style={styles.monthlySales}>₹{item.sales.toLocaleString()}</Text>
              </View>

              <View style={styles.monthlyProgressBar}>
                <View
                  style={[
                    styles.monthlyProgress,
                    {
                      width: `${Math.min((item.sales / (retailer.target || 150000)) * 100, 100)}%`,
                      backgroundColor: index % 2 === 0 ? '#6366f1' : '#10b981'
                    }
                  ]}
                />
              </View>

              <Text style={styles.monthlyOrders}>{item.orders} orders</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Render category performance
  const renderCategoryPerformance = () => {
    if (filteredData.length === 0 || !filteredData[0]?.categoryPerformance) return renderEmptyList();

    const retailer = filteredData[0];
    if (!retailer) return renderEmptyList();
    const categoryData = retailer.categoryPerformance || [];

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressCard}>
          <Text style={styles.progressTitle}>Sales by Category</Text>

          {categoryData.map((item: { category: string; sales: number; percentage: number }, index: number) => (
            <View key={index} style={styles.categoryItem}>
              <View style={styles.categoryHeader}>
                <Text style={styles.categoryName}>{item.category}</Text>
                <Text style={styles.categorySales}>₹{item.sales.toLocaleString()}</Text>
              </View>

              <View style={styles.categoryProgressBar}>
                <View
                  style={[
                    styles.categoryProgress,
                    {
                      width: `${item.percentage}%`,
                      backgroundColor:
                        index === 0 ? '#6366f1' :
                        index === 1 ? '#10b981' : '#f59e0b'
                    }
                  ]}
                />
              </View>

              <Text style={styles.categoryPercentage}>{item.percentage}% of total sales</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Render incentives
  const renderIncentives = () => {
    if (filteredData.length === 0 || !filteredData[0]?.incentives) return renderEmptyList();

    const retailer = filteredData[0];
    if (!retailer) return renderEmptyList();
    const incentivesData = retailer.incentives || [];

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressCard}>
          <Text style={styles.progressTitle}>Your Incentives</Text>

          {incentivesData.map((item: { name: string; amount: number; status: string }, index: number) => (
            <View key={index} style={styles.incentiveItem}>
              <View style={styles.incentiveHeader}>
                <Text style={styles.incentiveName}>{item.name}</Text>
                <View style={[
                  styles.incentiveStatus,
                  {
                    backgroundColor:
                      item.status === 'paid' ? '#10b981' :
                      item.status === 'approved' ? '#f59e0b' : '#6b7280'
                  }
                ]}>
                  <Text style={styles.incentiveStatusText}>
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                  </Text>
                </View>
              </View>

              <Text style={styles.incentiveAmount}>₹{item.amount.toLocaleString()}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Render retailer content based on active tab
  const renderRetailerContent = () => {
    switch (activeTab) {
      case 'progress':
        return renderRetailerProgress();
      case 'monthly':
        return renderMonthlyPerformance();
      case 'category':
        return renderCategoryPerformance();
      case 'incentives':
        return renderIncentives();
      default:
        return null;
    }
  };

  // Show login prompt for public users
  if (!currentUser || currentUser.role === UserRole.PUBLIC) {
    return (
      <View style={styles.emptyContainer}>
        <Icon name="lock" size={64} color="#e5e7eb" />
        <Text style={styles.emptyText}>Leaderboard is only available for logged-in users</Text>
        <TouchableOpacity
          style={styles.loginButton}
          onPress={() => useNavigation().navigate('Login' as never)}
        >
          <Text style={styles.loginButtonText}>Log In to View Leaderboard</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
          <Text style={styles.loadingText}>Loading leaderboard data...</Text>
        </View>
      ) : (
        currentUser?.role === UserRole.RETAILER ? (
          <>
            {renderHeader()}
            {renderRetailerContent()}
          </>
        ) : (
          <FlatList
            data={filteredData}
            renderItem={renderItem}
            keyExtractor={(item) => item.userId}
            ListHeaderComponent={renderHeader}
            ListEmptyComponent={renderEmptyList}
            contentContainerStyle={styles.listContent}
          />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  // Monthly performance styles
  monthlyItem: {
    marginBottom: 16,
  },
  monthlyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  monthlyMonth: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
  },
  monthlySales: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#10b981',
  },
  monthlyProgressBar: {
    height: 10,
    backgroundColor: '#f3f4f6',
    borderRadius: 5,
    marginVertical: 4,
    overflow: 'hidden',
  },
  monthlyProgress: {
    height: '100%',
    borderRadius: 5,
  },
  monthlyOrders: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'right',
  },

  // Category performance styles
  categoryItem: {
    marginBottom: 16,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
  },
  categorySales: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#10b981',
  },
  categoryProgressBar: {
    height: 12,
    backgroundColor: '#f3f4f6',
    borderRadius: 6,
    marginVertical: 4,
    overflow: 'hidden',
  },
  categoryProgress: {
    height: '100%',
    borderRadius: 6,
  },
  categoryPercentage: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'right',
  },

  // Incentive styles
  incentiveItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  incentiveHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  incentiveName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
    flex: 1,
  },
  incentiveStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  incentiveStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'white',
  },
  incentiveAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#10b981',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
  },
  progressContainer: {
    padding: 16,
    backgroundColor: '#f9fafb',
  },
  progressCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    textAlign: 'center',
  },
  progressRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 20,
  },
  progressItem: {
    alignItems: 'center',
  },
  progressValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#10b981',
    marginBottom: 4,
  },
  progressLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  progressDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#e5e7eb',
  },
  rankIndicator: {
    backgroundColor: '#e0e7ff',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignSelf: 'center',
  },
  rankIndicatorText: {
    color: '#6366f1',
    fontWeight: 'bold',
    fontSize: 16,
  },
  targetContainer: {
    marginTop: 10,
  },
  targetProgressBar: {
    height: 16,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
  },
  targetProgress: {
    height: '100%',
    backgroundColor: '#6366f1',
    borderRadius: 8,
  },
  targetDetails: {
    marginTop: 10,
  },
  targetRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  targetLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  targetValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
  },
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  headerContainer: {
    backgroundColor: '#6366f1',
    padding: 20,
    paddingBottom: 0,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    marginTop: 20,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: 'white',
  },
  tabText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '500',
  },
  activeTabText: {
    color: 'white',
    fontWeight: 'bold',
  },
  listContent: {
    paddingBottom: 20,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 12,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  rankContainer: {
    width: 30,
    alignItems: 'center',
  },
  rankText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  placeholderAvatar: {
    backgroundColor: '#6366f1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  infoContainer: {
    flex: 1,
  },
  nameText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  roleText: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  statsContainer: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#10b981',
  },
  countText: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: '#9ca3af',
    textAlign: 'center',
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LeaderboardScreen;
