import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
  Image,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Searchbar,
  Chip,
  useTheme,
  IconButton,
  Dialog,
  Portal,
  TextInput,
  Divider,
  ActivityIndicator,
  Menu,
} from 'react-native-paper';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import Video from 'react-native-video';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import DocumentPicker from 'react-native-document-picker';

// Announcement interface
interface Announcement {
  id: string;
  title: string;
  content: string;
  date: string;
  author: string;
  targetRoles: UserRole[];
  isPinned: boolean;
  images?: string[];
  video?: string;
}

// Media type for handling images and videos
interface Media {
  uri: string;
  type: 'image' | 'video';
  name?: string;
}

const mockAnnouncements: Announcement[] = [
  {
    id: '1',
    title: 'New Product Launch',
    content: 'We are excited to announce the launch of our new product line coming next month.',
    date: '2023-05-15',
    author: 'Ooge Team',
    targetRoles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER],
    isPinned: true,
    images: [
      'https://m.media-amazon.com/images/I/61iHi7VwQJL.jpg',
      'https://m.media-amazon.com/images/I/61t-juDPT+L.jpg'
    ],
    video: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
  },
  {
    id: '2',
    title: 'Price Update for Q2',
    content: 'Please note that there will be a price adjustment for select products starting from next quarter.',
    date: '2023-04-28',
    author: 'Ooge Team',
    targetRoles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST],
    isPinned: false,
    images: [
      'https://m.media-amazon.com/images/I/71oxdhd58TL.jpg'
    ]
  },
  {
    id: '3',
    title: 'System Maintenance',
    content: 'The system will be down for maintenance on Sunday from 2 AM to 5 AM.',
    date: '2023-04-20',
    author: 'Ooge Team',
    targetRoles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER],
    isPinned: false
  },
  {
    id: '4',
    title: 'New Distributor Onboarding',
    content: 'Welcome our new distributors in the Northern region. Please reach out to help them get started.',
    date: '2023-04-15',
    author: 'Ooge Team',
    targetRoles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST],
    isPinned: false,
    video: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
  },
  {
    id: '5',
    title: 'Holiday Schedule',
    content: 'Please note that our offices will be closed during the upcoming holidays.',
    date: '2023-04-10',
    author: 'Ooge Team',
    targetRoles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER],
    isPinned: false,
    images: [
      'https://m.media-amazon.com/images/I/61q3y6HA05L.jpg'
    ]
  },
];

const AnnouncementScreen: React.FC = () => {
  const { currentUser } = useUser();
  const theme = useTheme();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterPinned, setFilterPinned] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null);
  const [showMediaDialog, setShowMediaDialog] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoLoading, setVideoLoading] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const videoRef = useRef<Video>(null);
  const [mediaMenuVisible, setMediaMenuVisible] = useState(false);
  const [capturedMedia, setCapturedMedia] = useState<Media[]>([]);
  const [capturedVideo, setCapturedVideo] = useState<Media | null>(null);
  
  const [newAnnouncement, setNewAnnouncement] = useState({
    title: '',
    content: '',
    targetRoles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER],
    images: [] as string[],
    video: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Load announcements
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      // Filter announcements based on user role
      const userRole = currentUser?.role || UserRole.PUBLIC;
      const filteredAnnouncements = mockAnnouncements.filter(
        announcement => announcement.targetRoles.includes(userRole)
      );
      
      setAnnouncements(filteredAnnouncements);
      setIsLoading(false);
    }, 1000);
  }, [currentUser]);

  // Reset video state when dialog closes
  useEffect(() => {
    if (!showMediaDialog) {
      setIsVideoPlaying(false);
      setVideoLoading(false);
      setVideoError(false);
    }
  }, [showMediaDialog]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!dialogVisible) {
      resetForm();
    }
  }, [dialogVisible]);

  // Reset form
  const resetForm = () => {
    setNewAnnouncement({
      title: '',
      content: '',
      targetRoles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER],
      images: [],
      video: ''
    });
    setCapturedMedia([]);
    setCapturedVideo(null);
    setFormErrors({});
  };

  // Check if user can create announcements
  const canCreateAnnouncement = (): boolean => {
    if (!currentUser) return false;
    return [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role);
  };

  // Filter announcements based on search and pinned status
  const getFilteredAnnouncements = () => {
    return announcements.filter(announcement => {
      const matchesSearch = !searchQuery || 
        announcement.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        announcement.content.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesPinned = !filterPinned || announcement.isPinned;
      
      return matchesSearch && matchesPinned;
    });
  };

  const filteredAnnouncements = getFilteredAnnouncements();

  // Request camera and storage permissions (Android)
  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const cameraGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: "Camera Permission",
            message: "App needs access to your camera to take photos and videos",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK"
          }
        );
        
        const storageGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: "Storage Permission",
            message: "App needs access to your storage to save photos and videos",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK"
          }
        );
        
        return (
          cameraGranted === PermissionsAndroid.RESULTS.GRANTED &&
          storageGranted === PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true; // iOS handles permissions differently
  };

  // Take photo using camera
  const takePhoto = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      Alert.alert('Permission Denied', 'Camera permission is required to take photos');
      return;
    }

    try {
      const result = await launchCamera({
        mediaType: 'photo',
        quality: 0.8,
        saveToPhotos: true,
      });

      if (result.didCancel) return;
      if (result.errorCode) {
        Alert.alert('Error', result.errorMessage || 'Failed to capture image');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset.uri) {
          const newMedia: Media = {
            uri: asset.uri,
            type: 'image',
            name: asset.fileName || `image_${Date.now()}.jpg`,
          };
          setCapturedMedia([...capturedMedia, newMedia]);
        }
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to capture image');
    }
  };

  // Record video using camera
  const recordVideo = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      Alert.alert('Permission Denied', 'Camera permission is required to record videos');
      return;
    }

    try {
      const result = await launchCamera({
        mediaType: 'video',
        videoQuality: 'medium',
        durationLimit: 60, // 1 minute max
        saveToPhotos: true,
      });

      if (result.didCancel) return;
      if (result.errorCode) {
        Alert.alert('Error', result.errorMessage || 'Failed to record video');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset.uri) {
          const newMedia: Media = {
            uri: asset.uri,
            type: 'video',
            name: asset.fileName || `video_${Date.now()}.mp4`,
          };
          setCapturedVideo(newMedia);
        }
      }
    } catch (error) {
      console.error('Error recording video:', error);
      Alert.alert('Error', 'Failed to record video');
    }
  };

  // Pick image from gallery
  const pickImage = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: 'photo',
        quality: 0.8,
        selectionLimit: 5, // Allow multiple selection
      });

      if (result.didCancel) return;
      if (result.errorCode) {
        Alert.alert('Error', result.errorMessage || 'Failed to pick image');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const newMedia: Media[] = result.assets.map(asset => ({
          uri: asset.uri!,
          type: 'image',
          name: asset.fileName || `image_${Date.now()}.jpg`,
        }));
        setCapturedMedia([...capturedMedia, ...newMedia]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  // Pick video from gallery
  const pickVideo = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: 'video',
        videoQuality: 'medium',
        selectionLimit: 1,
      });

      if (result.didCancel) return;
      if (result.errorCode) {
        Alert.alert('Error', result.errorMessage || 'Failed to pick video');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset.uri) {
          const newMedia: Media = {
            uri: asset.uri,
            type: 'video',
            name: asset.fileName || `video_${Date.now()}.mp4`,
          };
          setCapturedVideo(newMedia);
        }
      }
    } catch (error) {
      console.error('Error picking video:', error);
      Alert.alert('Error', 'Failed to pick video');
    }
  };

  // Pick document (for larger files)
  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.video, DocumentPicker.types.images],
      });

      if (result[0].type?.startsWith('image/')) {
        const newMedia: Media = {
          uri: result[0].uri,
          type: 'image',
          name: result[0].name,
        };
        setCapturedMedia([...capturedMedia, newMedia]);
      } else if (result[0].type?.startsWith('video/')) {
        const newMedia: Media = {
          uri: result[0].uri,
          type: 'video',
          name: result[0].name,
        };
        setCapturedVideo(newMedia);
      }
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
                console.error('Error picking document:', error);
        Alert.alert('Error', 'Failed to pick document');
      }
    }
  };

  // Handle creating a new announcement
  const handleCreateAnnouncement = async () => {
    // Validate form
    const errors: Record<string, string> = {};
    if (!newAnnouncement.title.trim()) {
      errors.title = 'Title is required';
    }
    if (!newAnnouncement.content.trim()) {
      errors.content = 'Content is required';
    }
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    // Show loading indicator
    setIsLoading(true);

    try {
      // Process images
      const imageUris: string[] = [];
      for (const media of capturedMedia) {
        // In a real app, you would upload these to a server and get back URLs
        // For now, we'll just use the local URIs
        imageUris.push(media.uri);
      }

      // Process video
      let videoUri = '';
      if (capturedVideo) {
        // In a real app, you would upload this to a server and get back a URL
        videoUri = capturedVideo.uri;
      }

      // Create new announcement object
      const newAnnouncementObj: Announcement = {
        id: Date.now().toString(),
        title: newAnnouncement.title,
        content: newAnnouncement.content,
        date: new Date().toISOString().split('T')[0],
        author: currentUser?.name || 'Unknown',
        targetRoles: newAnnouncement.targetRoles,
        isPinned: false,
      };

      // Add media if provided
      if (imageUris.length > 0) {
        newAnnouncementObj.images = imageUris;
      }
      
      if (videoUri) {
        newAnnouncementObj.video = videoUri;
      }

      // Add to announcements list (in a real app, this would be an API call)
      setAnnouncements([newAnnouncementObj, ...announcements]);
      
      // Close dialog and reset form
      setDialogVisible(false);
      resetForm();

      // Show success message
      Alert.alert('Success', 'Announcement created successfully');
    } catch (error) {
      console.error('Error creating announcement:', error);
      Alert.alert('Error', 'Failed to create announcement');
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle pin status
  const togglePin = (id: string) => {
    setAnnouncements(
      announcements.map(announcement => 
        announcement.id === id 
          ? { ...announcement, isPinned: !announcement.isPinned } 
          : announcement
      )
    );
  };

  // Delete announcement
  const deleteAnnouncement = (id: string) => {
    Alert.alert(
      'Delete Announcement',
      'Are you sure you want to delete this announcement?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          onPress: () => {
            setAnnouncements(announcements.filter(a => a.id !== id));
          },
          style: 'destructive'
        }
      ]
    );
  };

  // Remove image from captured media
  const removeMedia = (index: number) => {
    const updatedMedia = [...capturedMedia];
    updatedMedia.splice(index, 1);
    setCapturedMedia(updatedMedia);
  };

  // Remove video
  const removeVideo = () => {
    setCapturedVideo(null);
  };

  // View media in full screen
  const viewMedia = (announcement: Announcement) => {
    setSelectedAnnouncement(announcement);
    setShowMediaDialog(true);
  };

  // Handle video playback
  const toggleVideoPlayback = () => {
    setIsVideoPlaying(!isVideoPlaying);
  };

  // Handle video load start
  const handleVideoLoadStart = () => {
    setVideoLoading(true);
    setVideoError(false);
  };

  // Handle video load
  const handleVideoLoad = () => {
    setVideoLoading(false);
  };

  // Handle video error
  const handleVideoError = () => {
    setVideoLoading(false);
    setVideoError(true);
  };

  // Render media preview
  const renderMediaPreview = (announcement: Announcement) => {
    return (
      <View style={styles.mediaContainer}>
        {announcement.images && announcement.images.length > 0 && (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imageScrollView}>
            {announcement.images.map((image, index) => (
              <TouchableOpacity 
                key={`${announcement.id}-img-${index}`}
                onPress={() => viewMedia(announcement)}
              >
                <Image 
                  source={{ uri: image }} 
                  style={styles.previewImage} 
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
        
        {announcement.video && (
          <TouchableOpacity 
            style={styles.videoPreviewContainer}
            onPress={() => viewMedia(announcement)}
          >
            <View style={styles.videoPlaceholder}>
              <IconButton icon="play-circle" size={40} iconColor={theme.colors.primary} />
              <Text style={styles.videoText}>Play Video</Text>
            </View>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Render announcement card
  const renderAnnouncementCard = ({ item }: { item: Announcement }) => {
    const canEdit = currentUser?.role === UserRole.OOGE_TEAM || 
                   (currentUser?.role === UserRole.SUPER_STOCKIST && item.author.includes(currentUser.name || ''));
    
    const hasMedia = (item.images && item.images.length > 0) || item.video;
    
    return (
      <Card style={[styles.card, item.isPinned && styles.pinnedCard]}>
        <Card.Title
          title={item.title}
          titleStyle={styles.cardTitle}
          subtitle={`${item.date} • ${item.author}`}
          subtitleStyle={styles.cardSubtitle}
          right={(props) => (
            <View style={styles.cardActions}>
              {canEdit && (
                <>
                  <IconButton
                    {...props}
                    icon={item.isPinned ? 'pin' : 'pin-outline'}
                    onPress={() => togglePin(item.id)}
                    iconColor={item.isPinned ? theme.colors.primary : undefined}
                  />
                  <IconButton
                    {...props}
                    icon="delete-outline"
                    onPress={() => deleteAnnouncement(item.id)}
                    iconColor={theme.colors.error}
                  />
                </>
              )}
            </View>
          )}
        />
        <Card.Content>
          <Text variant="bodyMedium" style={styles.cardContent}>{item.content}</Text>
          
          {hasMedia && (
            <>
              <Divider style={styles.divider} />
              {renderMediaPreview(item)}
            </>
          )}
        </Card.Content>
      </Card>
    );
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    canCreateAnnouncement() ? (
      <IconButton
        icon="plus"
        iconColor="white"
        size={24}
        onPress={() => setDialogVisible(true)}
      />
    ) : null
  );

  // Render content
  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading announcements...</Text>
        </View>
      );
    }

    if (filteredAnnouncements.length === 0) {
      return (
        <EmptyState
          icon="announcement"
          message={searchQuery ? 'No announcements match your search' : 'No announcements found'}
          actionLabel={canCreateAnnouncement() ? 'Create Announcement' : undefined}
          onAction={canCreateAnnouncement() ? () => setDialogVisible(true) : undefined}
        />
      );
    }

    return (
      <FlatList
        data={filteredAnnouncements}
        renderItem={renderAnnouncementCard}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.list}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  // Render media dialog
  const renderMediaDialog = () => {
    if (!selectedAnnouncement) return null;
    
    const screenWidth = Dimensions.get('window').width;
    const videoHeight = screenWidth * 0.5625; // 16:9 aspect ratio
    
    return (
      <Portal>
        <Dialog visible={showMediaDialog} onDismiss={() => setShowMediaDialog(false)} style={styles.mediaDialog}>
          <Dialog.Title>{selectedAnnouncement.title} - Media</Dialog.Title>
          <Dialog.ScrollArea>
            <ScrollView>
              {selectedAnnouncement.images && selectedAnnouncement.images.length > 0 && (
                <View style={styles.fullMediaContainer}>
                  {selectedAnnouncement.images.map((image, index) => (
                    <View key={`full-img-${index}`} style={styles.fullImageContainer}>
                      <Image 
                        source={{ uri: image }} 
                        style={styles.fullImage} 
                        resizeMode="contain"
                      />
                    </View>
                  ))}
                </View>
              )}
              
              {selectedAnnouncement.video && (
                <View style={styles.videoContainer}>
                  <TouchableOpacity 
                    style={[styles.videoPlayerContainer, { height: videoHeight }]}
                    onPress={toggleVideoPlayback}
                    activeOpacity={0.9}
                  >
                    <Video
                      ref={videoRef}
                      source={{ uri: selectedAnnouncement.video }}
                      style={styles.videoPlayer}
                      resizeMode="contain"
                      paused={!isVideoPlaying}
                      onLoadStart={handleVideoLoadStart}
                      onLoad={handleVideoLoad}
                      onError={handleVideoError}
                      controls={isVideoPlaying}
                      repeat
                    />
                    
                    {videoLoading && (
                      <View style={styles.videoOverlay}>
                        <ActivityIndicator size="large" color={theme.colors.primary} />
                      </View>
                    )}
                    
                    {videoError && (
                      <View style={styles.videoOverlay}>
                        <Text style={styles.videoErrorText}>Failed to load video</Text>
                      </View>
                    )}
                    
                    {!isVideoPlaying && !videoLoading && !videoError && (
                      <View style={styles.videoOverlay}>
                        <IconButton 
                          icon="play-circle" 
                          size={60} 
                          iconColor={theme.colors.primary} 
                        />
                      </View>
                    )}
                  </TouchableOpacity>
                </View>
              )}
            </ScrollView>
          </Dialog.ScrollArea>
          <Dialog.Actions>
            <Button onPress={() => setShowMediaDialog(false)}>Close</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    );
  };

  // Render media menu
  const renderMediaMenu = () => (
    <Menu
      visible={mediaMenuVisible}
      onDismiss={() => setMediaMenuVisible(false)}
      anchor={
        <Button 
          mode="outlined" 
          icon="image-plus" 
          onPress={() => setMediaMenuVisible(true)}
          style={styles.mediaButton}
        >
          Add Media
        </Button>
      }
    >
      <Menu.Item 
        leadingIcon="camera" 
        onPress={() => {
          setMediaMenuVisible(false);
          takePhoto();
        }} 
        title="Take Photo" 
      />
      <Menu.Item 
        leadingIcon="video" 
        onPress={() => {
          setMediaMenuVisible(false);
          recordVideo();
        }} 
        title="Record Video" 
      />
      <Menu.Item 
        leadingIcon="image-multiple" 
        onPress={() => {
          setMediaMenuVisible(false);
          pickImage();
        }} 
        title="Choose from Gallery" 
      />
      <Menu.Item 
        leadingIcon="file-video" 
        onPress={() => {
          setMediaMenuVisible(false);
          pickVideo();
        }} 
        title="Choose Video" 
      />
      <Menu.Item 
        leadingIcon="file" 
        onPress={() => {
          setMediaMenuVisible(false);
          pickDocument();
        }} 
        title="Choose Document" 
      />
    </Menu>
  );

  // Render create announcement dialog
  const renderCreateDialog = () => (
    <Portal>
      <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)} style={styles.createDialog}>
        <Dialog.Title>Create Announcement</Dialog.Title>
        <Dialog.ScrollArea>
          <ScrollView>
            <View style={styles.dialogContent}>
              <TextInput
                label="Title"
                value={newAnnouncement.title}
                onChangeText={text => {
                  setNewAnnouncement({...newAnnouncement, title: text});
                  if (formErrors.title) {
                    setFormErrors({...formErrors, title: ''});
                  }
                }}
                mode="outlined"
                style={styles.input}
                error={!!formErrors.title}
              />
              {formErrors.title && (
                <Text style={styles.errorText}>{formErrors.title}</Text>
              )}
              
              <TextInput
                label="Content"
                value={newAnnouncement.content}
                onChangeText={text => {
                  setNewAnnouncement({...newAnnouncement, content: text});
                  if (formErrors.content) {
                    setFormErrors({...formErrors, content: ''});
                  }
                }}
                mode="outlined"
                multiline
                numberOfLines={4}
                style={styles.input}
                error={!!formErrors.content}
              />
              {formErrors.content && (
                <Text style={styles.errorText}>{formErrors.content}</Text>
              )}
              
              <Text variant="bodyMedium" style={styles.sectionLabel}>Media:</Text>
              
              <View style={styles.mediaButtons}>
                {renderMediaMenu()}
              </View>
              
              {capturedMedia.length > 0 && (
                <View style={styles.previewContainer}>
                  <Text variant="bodyMedium" style={styles.previewLabel}>Images:</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {capturedMedia.map((media, index) => (
                      <View key={`new-img-${index}`} style={styles.previewImageContainer}>
                        <Image source={{ uri: media.uri }} style={styles.previewImage} />
                                                <IconButton
                          icon="close-circle"
                          size={20}
                          iconColor="white"
                          style={styles.removeButton}
                          onPress={() => removeMedia(index)}
                        />
                      </View>
                    ))}
                  </ScrollView>
                </View>
              )}
              
              {capturedVideo && (
                <View style={styles.previewContainer}>
                  <Text variant="bodyMedium" style={styles.previewLabel}>Video:</Text>
                  <View style={styles.videoPreview}>
                    <View style={styles.videoPreviewContent}>
                      <IconButton icon="video" size={24} iconColor={theme.colors.primary} />
                      <Text numberOfLines={1} style={styles.videoUrl}>
                        {capturedVideo.name || capturedVideo.uri.split('/').pop() || 'Video'}
                      </Text>
                    </View>
                    <IconButton
                      icon="close-circle"
                      size={20}
                      iconColor={theme.colors.error}
                      onPress={removeVideo}
                    />
                  </View>
                </View>
              )}
              
              {/* <Text variant="bodyMedium" style={styles.sectionLabel}>Target Audience:</Text>
              <View style={styles.targetRoles}>
                {Object.values(UserRole).filter(role => role !== UserRole.PUBLIC).map(role => (
                  <Chip
                    key={role}
                    selected={newAnnouncement.targetRoles.includes(role)}
                    onPress={() => {
                      const updatedRoles = newAnnouncement.targetRoles.includes(role)
                        ? newAnnouncement.targetRoles.filter(r => r !== role)
                        : [...newAnnouncement.targetRoles, role];
                      setNewAnnouncement({...newAnnouncement, targetRoles: updatedRoles});
                    }}
                    style={styles.roleChip}
                    selectedColor={theme.colors.primary}
                  >
                    {role === UserRole.OOGE_TEAM ? 'Admin' : 
                     role === UserRole.SUPER_STOCKIST ? 'Super Stockist' : 
                     role === UserRole.DISTRIBUTOR ? 'Distributor' : 'Retailer'}
                  </Chip>
                ))}
              </View> */}
            </View>
          </ScrollView>
        </Dialog.ScrollArea>
        <Dialog.Actions>
          <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
          <Button 
            onPress={handleCreateAnnouncement} 
            mode="contained"
            loading={isLoading}
            disabled={isLoading}
          >
            Create
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );

  return (
    <BaseManagementScreen
      title="Announcements"
      showBack={false}
      rightActions={renderHeaderRightActions()}
      isLoading={false}
    >
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search announcements..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
        />
      </View>

      <View style={styles.filtersContainer}>
        <Chip
          selected={filterPinned}
          onPress={() => setFilterPinned(!filterPinned)}
          style={styles.chip}
          selectedColor={theme.colors.primary}
        >
          Pinned Only
        </Chip>
      </View>

      {renderContent()}
      {renderMediaDialog()}
      {renderCreateDialog()}

      {canCreateAnnouncement() && (
        <FAB
          icon="plus"
          style={[styles.fab, { backgroundColor: theme.colors.primary }]}
          onPress={() => setDialogVisible(true)}
          color="white"
        />
      )}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
    borderRadius: 8,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  chip: {
    marginRight: 8,
    borderRadius: 20,
  },
  list: {
    padding: 16,
    paddingBottom: 80,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  pinnedCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#6366f1',
  },
  cardTitle: {
    fontWeight: 'bold',
  },
  cardSubtitle: {
    fontSize: 12,
  },
  cardContent: {
    lineHeight: 20,
  },
  cardActions: {
    flexDirection: 'row',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    borderRadius: 28,
    elevation: 4,
  },
  input: {
    marginBottom: 8,
    backgroundColor: 'white',
  },
  errorText: {
    color: '#ef4444',
    marginBottom: 8,
    marginLeft: 8,
    fontSize: 12,
  },
  sectionLabel: {
    marginTop: 16,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  targetRoles: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  roleChip: {
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 20,
  },
  mediaContainer: {
    marginTop: 12,
  },
  imageScrollView: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  previewImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 8,
  },
  videoPreviewContainer: {
    height: 120,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  videoPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoText: {
    marginTop: 4,
    color: '#666',
  },
  videoUrl: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    marginLeft: 8,
  },
  divider: {
    marginTop: 12,
  },
  dialogContent: {
    padding: 16,
  },
  mediaButtons: {
    flexDirection: 'row',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  mediaButton: {
    marginRight: 8,
    marginBottom: 8,
  },
  previewContainer: {
    marginBottom: 16,
  },
  previewLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  previewImageContainer: {
    position: 'relative',
    marginRight: 8,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    margin: 0,
  },
  videoPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 8,
    justifyContent: 'space-between',
  },
  videoPreviewContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  mediaDialog: {
    maxHeight: '90%',
    borderRadius: 12,
  },
  createDialog: {
    maxHeight: '90%',
    borderRadius: 12,
  },
  fullMediaContainer: {
    marginBottom: 16,
  },
  fullImageContainer: {
    marginBottom: 16,
  },
  fullImage: {
    width: '100%',
    height: 250,
    borderRadius: 8,
  },
  videoContainer: {
    marginBottom: 16,
  },
  videoPlayerContainer: {
    width: '100%',
    backgroundColor: '#000',
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  videoPlayer: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  videoErrorText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
});

export default AnnouncementScreen;