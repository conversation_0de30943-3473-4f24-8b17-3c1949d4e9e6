import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  Surface,
  IconButton,
  Switch,
  Chip,
} from 'react-native-paper';

import { useUser } from '../../context/UserContext';
import { UserRole, User } from '../../data/mockData';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import UserSelectionModal from '../../components/management/UserSelectionModal';
import ProductSelectionModal from '../../components/management/ProductSelectionModal';
import { useGetAllUsersQuery } from './api/apiSlice';
import {
  useCreateDistributorPriceMutation,
  useCreateRetailerPriceMutation,
  DistributorPriceRequest,
  RetailerPriceRequest,
} from './api/price';

type RootStackParamList = {
  CreatePricing: { parentId?: string; childRole: UserRole };
  PricingList: undefined;
  PricingManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: User[] };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'CreatePricing'>;
type CreatePricingRouteProp = RouteProp<RootStackParamList, 'CreatePricing'>;

interface ProductItem {
  id: string;
  name: string;
  productCode?: string;
  category: string;
}

const CreatePricingScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<CreatePricingRouteProp>();
  const { currentUser } = useUser();
  const theme = useTheme();

  // Extract route params
  const { parentId, childRole } = route.params || {};

  // Form state
  const [pricingName, setPricingName] = useState('');
  const [description, setDescription] = useState('');
  const [discountPercentage, setDiscountPercentage] = useState('');
  const [isGlobal, setIsGlobal] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<ProductItem[]>([]);
  const [isUserModalVisible, setIsUserModalVisible] = useState(false);
  const [isProductModalVisible, setIsProductModalVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // API hooks
  const {
    data: allApiUsers = [],
    isLoading: isLoadingUsers,
    error: errorUsers,
    refetch: refetchUsers,
  } = useGetAllUsersQuery(currentUser?.id);

  const [createDistributorPrice, { isLoading: isCreatingDistributorPrice }] = useCreateDistributorPriceMutation();
  const [createRetailerPrice, { isLoading: isCreatingRetailerPrice }] = useCreateRetailerPriceMutation();

  // Filter users based on child role
  const getFilteredUsers = (): User[] => {
    if (!allApiUsers) return [];
    
    return allApiUsers.filter((user: any) => {
      // Convert API user role to our UserRole enum
      let userRole: UserRole;
      switch (user.role?.toLowerCase()) {
        case 'super_stockist':
        case 'super stockist':
          userRole = UserRole.SUPER_STOCKIST;
          break;
        case 'distributor':
          userRole = UserRole.DISTRIBUTOR;
          break;
        case 'retailer':
          userRole = UserRole.RETAILER;
          break;
        case 'ooge_team':
        case 'ooge team':
          userRole = UserRole.OOGE_TEAM;
          break;
        default:
          userRole = UserRole.PUBLIC;
      }
      
      return userRole === childRole;
    }).map((user: any) => ({
      id: user.id.toString(),
      name: user.name || user.username,
      role: childRole,
      email: user.email,
      phone: user.phone,
    }));
  };

  const filteredUsers = getFilteredUsers();

  // Get role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  const getPricingType = (): 'DISTRIBUTOR' | 'RETAILER' => {
    return childRole === UserRole.DISTRIBUTOR ? 'DISTRIBUTOR' : 'RETAILER';
  };

  // Validation
  const validateForm = (): boolean => {
    if (!pricingName.trim()) {
      Alert.alert('Error', 'Please enter a pricing name');
      return false;
    }

    if (!description.trim()) {
      Alert.alert('Error', 'Please enter a description');
      return false;
    }

    const discount = parseFloat(discountPercentage);
    if (isNaN(discount) || discount <= 0 || discount > 100) {
      Alert.alert('Error', 'Please enter a valid discount percentage (1-100)');
      return false;
    }

    if (!isGlobal && selectedUsers.length === 0) {
      Alert.alert('Error', 'Please select users or enable global pricing');
      return false;
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const discount = parseFloat(discountPercentage);
      
      // Prepare catalog IDs mapping (for now, use empty object for all products)
      const catalogIds = selectedProducts.length > 0 
        ? selectedProducts.reduce((acc, product) => {
            acc[product.id] = `${discount}%`;
            return acc;
          }, {} as {[key: string]: string})
        : {};

      if (getPricingType() === 'DISTRIBUTOR') {
        const payload: DistributorPriceRequest = {
          id: Number(currentUser?.id),
          distributorIds: isGlobal ? [-1] : selectedUsers.map(user => Number(user.id)),
          catalogIds: selectedProducts.length > 0 ? [catalogIds] : undefined,
          globalPercentage: isGlobal && selectedProducts.length === 0 ? `${discount}%` : undefined,
        };

        console.log('🚀 [CREATE PRICING] Creating Distributor Price:', payload);

        const response = await createDistributorPrice({
          data: payload,
          isGlobal: isGlobal
        }).unwrap();

        console.log('✅ [CREATE PRICING] Distributor pricing created:', response);

      } else {
        const payload: RetailerPriceRequest = {
          id: Number(currentUser?.id),
          retailerIds: isGlobal ? [-1] : selectedUsers.map(user => Number(user.id)),
          catalogIds: selectedProducts.length > 0 ? [catalogIds] : undefined,
          globalPercentage: isGlobal && selectedProducts.length === 0 ? `${discount}%` : undefined,
        };

        console.log('🚀 [CREATE PRICING] Creating Retailer Price:', payload);

        const response = await createRetailerPrice({
          data: payload,
          isGlobal: isGlobal
        }).unwrap();

        console.log('✅ [CREATE PRICING] Retailer pricing created:', response);
      }

      Alert.alert(
        'Success',
        'Pricing rule created successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );

    } catch (error: any) {
      console.error('❌ [CREATE PRICING] Failed to create pricing:', error);
      
      let errorMessage = 'Failed to create pricing rule. Please try again.';
      if (error?.data) {
        if (typeof error.data === 'string') {
          errorMessage = error.data;
        } else if (error.data.message) {
          errorMessage = error.data.message;
        }
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Navigate to product-based pricing creation
  const handleCreateWithProducts = () => {
    navigation.navigate('PricingManagement', {
      userId: currentUser?.id,
      userName: currentUser?.name,
      userRole: currentUser?.role,
      selectedUsers: isGlobal ? [] : selectedUsers,
    });
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    <IconButton
      icon="check"
      iconColor="white"
      size={24}
      onPress={handleSubmit}
      disabled={isSubmitting || isCreatingDistributorPrice || isCreatingRetailerPrice}
    />
  );

  // Remove user from selection
  const removeUser = (userId: string) => {
    setSelectedUsers(prev => prev.filter(user => user.id !== userId));
  };

  // Remove product from selection
  const removeProduct = (productId: string) => {
    setSelectedProducts(prev => prev.filter(product => product.id !== productId));
  };

  return (
    <BaseManagementScreen
      title="Create Pricing Rule"
      showBack={true}
      rightActions={renderHeaderRightActions()}
      subtitle={`New ${getRoleName(childRole)} Pricing`}
      isLoading={isSubmitting || isCreatingDistributorPrice || isCreatingRetailerPrice}
      loadingText="Creating pricing rule..."
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        
        {/* Basic Information */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>Basic Information</Text>
          
          <TextInput
            mode="outlined"
            label="Pricing Name *"
            placeholder="Enter pricing rule name"
            value={pricingName}
            onChangeText={setPricingName}
            style={styles.input}
          />

          <TextInput
            mode="outlined"
            label="Description *"
            placeholder="Enter description"
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={3}
            style={styles.input}
          />

          <TextInput
            mode="outlined"
            label="Discount Percentage *"
            placeholder="Enter discount percentage"
            value={discountPercentage}
            onChangeText={setDiscountPercentage}
            keyboardType="numeric"
            right={<TextInput.Affix text="%" />}
            style={styles.input}
          />
        </Surface>

        {/* Pricing Type */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>Pricing Type</Text>
          
          <View style={styles.switchContainer}>
            <View style={styles.switchContent}>
              <Text variant="bodyLarge">Global Pricing</Text>
              <Text variant="bodySmall" style={styles.switchDescription}>
                Apply to all {getRoleName(childRole).toLowerCase()}s
              </Text>
            </View>
            <Switch
              value={isGlobal}
              onValueChange={setIsGlobal}
              trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
              thumbColor={isGlobal ? '#6366f1' : '#f4f4f5'}
            />
          </View>
        </Surface>

        {/* User Selection */}
        {!isGlobal && (
          <Surface style={styles.section} elevation={1}>
            <View style={styles.sectionHeader}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Select {getRoleName(childRole)}s
              </Text>
              <Button
                mode="outlined"
                onPress={() => setIsUserModalVisible(true)}
                icon="plus"
                disabled={isLoadingUsers}
              >
                Add Users
              </Button>
            </View>

            {selectedUsers.length > 0 && (
              <View style={styles.chipContainer}>
                {selectedUsers.map((user) => (
                  <Chip
                    key={user.id}
                    mode="flat"
                    style={styles.chip}
                    onClose={() => removeUser(user.id)}
                  >
                    {user.name}
                  </Chip>
                ))}
              </View>
            )}

            {selectedUsers.length === 0 && (
              <Text style={styles.emptyText}>
                No {getRoleName(childRole).toLowerCase()}s selected
              </Text>
            )}
          </Surface>
        )}

        {/* Quick Actions */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>Quick Actions</Text>
          
          <Button
            mode="contained"
            onPress={handleCreateWithProducts}
            icon="shopping-cart"
            style={styles.actionButton}
          >
            Create with Product Selection
          </Button>
          
          <Text variant="bodySmall" style={styles.actionDescription}>
            Create pricing rules with specific product selection and individual pricing controls
          </Text>
        </Surface>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={isSubmitting || isCreatingDistributorPrice || isCreatingRetailerPrice}
            disabled={isSubmitting || isCreatingDistributorPrice || isCreatingRetailerPrice}
            style={styles.submitButton}
          >
            Create Pricing Rule
          </Button>
        </View>

      </ScrollView>

      {/* User Selection Modal */}
      <UserSelectionModal
        visible={isUserModalVisible}
        onDismiss={() => setIsUserModalVisible(false)}
        users={filteredUsers}
        selectedUsers={selectedUsers}
        onSelectionChange={setSelectedUsers}
        title={`Select ${getRoleName(childRole)}s`}
        isLoading={isLoadingUsers}
      />

    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: 'bold',
    color: '#374151',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchContent: {
    flex: 1,
  },
  switchDescription: {
    color: '#6b7280',
    marginTop: 4,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 8,
  },
  emptyText: {
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 16,
  },
  actionButton: {
    marginBottom: 8,
  },
  actionDescription: {
    color: '#6b7280',
    textAlign: 'center',
  },
  submitContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  submitButton: {
    paddingVertical: 8,
  },
});

export default CreatePricingScreen;
