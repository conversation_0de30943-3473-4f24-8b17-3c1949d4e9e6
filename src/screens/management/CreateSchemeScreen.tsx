import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Switch,
  Modal,
  FlatList,
  Platform,
  Image,
  PermissionsAndroid,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { launchImageLibrary, launchCamera, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import { useGetCategoriesQuery, useGetProductsQuery } from '../../services/api/apiSlice';
import {
  useCreateSchemeMutation,
  useUpdateSchemeMutation,
  useGetSchemesByUserIdQuery,
  CreateSchemeRequest,
  UpdateSchemeRequest,
  Scheme,
} from './api/scheme';
import { useGetAllUsersQuery } from './api/apiSlice';

// API Data Interfaces
interface CategoryItem {
  id: number;
  name: string;
  description: string;
  type: number;
  status: number;
  parentId: number;
  parentType: string;
  catalogType: string;
  urls?: string[];
}

interface ProductItem {
  id: number;
  productCode: string;
  name: string;
  type: number;
  status: number;
  description: string;
  parentId: number;
  brand: number;
  createdBy: number;
  label: string;
}

type RootStackParamList = {
  CreateScheme: { 
    parentId?: string; 
    childRole: UserRole; 
    selectedUsers?: any[]; 
    schemeId?: number;
    userIds?: number[];
  };
  EditScheme: { schemeId: number; selectedUsers?: any[] };
  SchemeManagement: undefined;
};

interface User {
  id: number;
  firstName: string;
  lastName:string;
  email: string;
  role: UserRole;
  parentId?: number;
  status: number;
}

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;
type CreateSchemeRouteProp = RouteProp<RootStackParamList, 'CreateScheme'>;

const CreateSchemeScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<CreateSchemeRouteProp>();
  const { currentUser } = useUser();

  // Extract route params including userIds
  const { 
    schemeId, 
    selectedUsers, 
    userIds: routeUserIds,
    childRole: routeChildRole 
  } = route.params || {};

  const isEditMode = !!schemeId;
  const isBulkMode = selectedUsers && selectedUsers.length > 0;

  // Safely extract route params with defaults
  const childRole = routeChildRole || (() => {
    if (currentUser?.role === UserRole.OOGE_TEAM) return UserRole.SUPER_STOCKIST;
    if (currentUser?.role === UserRole.SUPER_STOCKIST) return UserRole.DISTRIBUTOR;
    if (currentUser?.role === UserRole.DISTRIBUTOR) return UserRole.RETAILER;
    return UserRole.RETAILER;
  })();

  // API queries for categories and products
  const {
    data: categoriesData = [],
    isLoading: isCategoriesLoading,
    error: categoriesError
  } = useGetCategoriesQuery({ 
    page: 0, 
    count: 100, 
    type: 1, 
    status: 1 
  });

  const {
    data: allProductsData = [],
    isLoading: isAllProductsLoading,
    error: allProductsError
  } = useGetProductsQuery({ 
    page: 0, 
    count: 1000,
    type: 2, 
    status: 1 
  });

  const {
  data: usersData = [],
  isLoading: isUsersLoading,
  error: usersError
} = useGetAllUsersQuery(currentUser?.id);

  // API hooks
  const [createScheme, { isLoading: isCreating }] = useCreateSchemeMutation();
  const [updateScheme, { isLoading: isUpdating }] = useUpdateSchemeMutation();
  
  // For edit mode, fetch the scheme data
  const { data: schemeData, isLoading: isLoadingScheme } = useGetSchemesByUserIdQuery(
    parseInt(currentUser?.id || '0'),
    { skip: !isEditMode || !currentUser?.id }
  );

  const isLoading = isLoadingScheme || (isEditMode && isUpdating) || (!isEditMode && isCreating);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    discountType: 'gift' as 'gift' | 'trip',
    purchaseType: 'amount' as 'amount' | 'items', // New field for purchase type
    minQuantity: '',
    startDate: new Date(),
    endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
    applicabilityType: 'all' as 'all' | 'categories' | 'products', // Simplified applicability
    selectedProducts: [] as number[],
    selectedCategories: [] as number[],
    giftDescription: '',
    tripDescription: '',
    status: 1,
    schemeImages: [] as string[], // New field for images
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showGiftModal, setShowGiftModal] = useState(false);
  const [showTripModal, setShowTripModal] = useState(false);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [showUserSelector, setShowUserSelector] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Debug logging
  useEffect(() => {
    console.log('🔍 [CREATE SCHEME] API Data Debug:', {
      categoriesCount: categoriesData?.length || 0,
      allProductsCount: allProductsData?.length || 0,
      isCategoriesLoading,
      isAllProductsLoading,
      routeUserIds,
      selectedUsers: selectedUsers?.map(u => ({ id: u.id, name: u.name })),
    });
  }, [categoriesData, allProductsData, isCategoriesLoading, isAllProductsLoading, routeUserIds]);

  // Initialize form data for edit mode
  useEffect(() => {
    if (isEditMode && schemeData?.data && schemeId) {
      const scheme = schemeData.data.find((s: Scheme) => s.id === schemeId);
      if (scheme) {
        setFormData({
          name: scheme.name,
          description: scheme.description,
          discountType: scheme.offer.toLowerCase() as 'gift' | 'trip',
          purchaseType: 'amount', // Default to amount for existing schemes
          minQuantity: scheme.purchaseAmount.toString(),
          startDate: new Date(scheme.startDate),
          endDate: new Date(scheme.endDate),
          applicabilityType: 'all', // Default to all for existing schemes
          selectedProducts: [],
          selectedCategories: [],
          giftDescription: scheme.offer === 'GIFT' ? scheme.description : '',
          tripDescription: scheme.offer === 'TRIP' ? scheme.description : '',
          status: scheme.status,
          schemeImages: [],
        });
      }
    }
  }, [isEditMode, schemeData, schemeId]);

  // Get role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM: return 'Ooge Team';
      case UserRole.SUPER_STOCKIST: return 'Super Stockist';
      case UserRole.DISTRIBUTOR: return 'Distributor';
      case UserRole.RETAILER: return 'Retailer';
      default: return 'User';
    }
  };

  // Format date for display
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Update form data
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field if it exists
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle applicability type change
  const handleApplicabilityChange = (type: 'all' | 'categories' | 'products') => {
    setFormData(prev => ({
      ...prev,
      applicabilityType: type,
      selectedProducts: [],
      selectedCategories: []
    }));
  };

  // Toggle category selection
  const toggleCategorySelection = (categoryId: number) => {
    setFormData(prev => {
      const selectedCategories = [...prev.selectedCategories];
      const index = selectedCategories.indexOf(categoryId);
      
      if (index === -1) {
        selectedCategories.push(categoryId);
      } else {
        selectedCategories.splice(index, 1);
      }

      return {
        ...prev,
        selectedCategories,
        selectedProducts: [] // Clear products when categories change
      };
    });
  };

  // Select all categories
  const selectAllCategories = () => {
    const allCategoryIds = [-1];
    setFormData(prev => ({
      ...prev,
      selectedCategories: allCategoryIds,
      selectedProducts: []
    }));
  };

  // Clear all categories
  const clearAllCategories = () => {
    setFormData(prev => ({
      ...prev,
      selectedCategories: [],
      selectedProducts: []
    }));
  };

  // Toggle product selection
  const toggleProductSelection = (productId: number) => {
    setFormData(prev => {
      const selectedProducts = [...prev.selectedProducts];
      const index = selectedProducts.indexOf(productId);

      if (index === -1) {
        selectedProducts.push(productId);
      } else {
        selectedProducts.splice(index, 1);
      }

      return {
        ...prev,
        selectedProducts
      };
    });
  };

  // Select all products
  const selectAllProducts = () => {
    const filteredProducts = getFilteredProducts();
    const allProductIds = filteredProducts.map(p => p.id);
    setFormData(prev => ({
      ...prev,
      selectedProducts: allProductIds
    }));
  };

  // Clear all products
  const clearAllProducts = () => {
    setFormData(prev => ({
      ...prev,
      selectedProducts: []
    }));
  };

  // Get filtered products based on selected categories
  const getFilteredProducts = (): ProductItem[] => {
    if (!allProductsData || allProductsData.length === 0) {
      return [];
    }

    if (formData.selectedCategories.length === 0) {
      return allProductsData;
    }
    
    return allProductsData.filter((product: ProductItem) => 
      formData.selectedCategories.includes(product.parentId)
    );
  };

  // Add these helper functions before the render methods
  const toggleUserSelection = (userId: number) => {
    setSelectedUserIds(prev => {
      const index = prev.indexOf(userId);
      if (index === -1) {
        return [...prev, userId];
      } else {
        return prev.filter(id => id !== userId);
      }
    });
  };

  const selectAllUsers = () => {
    const filteredUsers = getFilteredUsers();
    const allUserIds = filteredUsers.map(u => u.id);
    setSelectedUserIds(allUserIds);
  };

  const clearAllUsers = () => {
    setSelectedUserIds([]);
  };

  const getFilteredUsers = (): User[] => {
    if (!usersData || usersData.length === 0) {
      return [];
    }

    if (!searchQuery.trim()) {
      return usersData;
    }

    return usersData.filter((user: User) => 
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getSelectedUsers = (): User[] => {
    return usersData.filter((user: User) => selectedUserIds.includes(user.id));
  };

  // Request camera permission
  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'App needs camera permission to take photos',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  // Handle image selection
  const handleImagePicker = () => {
    Alert.alert(
      'Select Image',
      'Choose an option',
      [
        { text: 'Camera', onPress: openCamera },
        { text: 'Gallery', onPress: openGallery },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('Permission Denied', 'Camera permission is required to take photos');
      return;
    }

    launchCamera(
      {
        mediaType: 'photo' as MediaType,
        quality: 0.8,
        maxWidth: 1000,
        maxHeight: 1000,
      },
      handleImageResponse
    );
  };

  const openGallery = () => {
    launchImageLibrary(
      {
        mediaType: 'photo' as MediaType,
        quality: 0.8,
        maxWidth: 1000,
        maxHeight: 1000,
        selectionLimit: 5, // Allow multiple images
      },
      handleImageResponse
    );
  };

  const handleImageResponse = (response: ImagePickerResponse) => {
    if (response.didCancel || response.errorMessage) {
      return;
    }

    if (response.assets && response.assets.length > 0) {
      const newImages = response.assets
        .filter(asset => asset.uri)
        .map(asset => asset.uri!);
      
      setFormData(prev => ({
        ...prev,
        schemeImages: [...prev.schemeImages, ...newImages].slice(0, 5) // Max 5 images
      }));
    }
  };

  // Remove image
  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      schemeImages: prev.schemeImages.filter((_, i) => i !== index)
    }));
  };

  // Update the validateForm function
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) errors.name = 'Scheme name is required';

    if (formData.discountType === 'gift' && !formData.giftDescription.trim()) {
      errors.giftDescription = 'Gift description is required';
    } else if (formData.discountType === 'trip' && !formData.tripDescription.trim()) {
      errors.tripDescription = 'Trip description is required';
    }

    if (!formData.minQuantity.trim()) {
      errors.minQuantity = `Minimum ${formData.purchaseType === 'amount' ? 'amount' : 'items'} is required`;
    } else if (isNaN(Number(formData.minQuantity)) || Number(formData.minQuantity) <= 0) {
      errors.minQuantity = `Minimum ${formData.purchaseType === 'amount' ? 'amount' : 'items'} must be a positive number`;
    }

    if (formData.startDate >= formData.endDate) {
      errors.endDate = 'End date must be after start date';
    }

    if (!currentUser?.id) {
      errors.general = 'User not authenticated';
    }

    // Check user selection - only if not in bulk mode from route params
    const userIds = selectedUserIds.length > 0 
      ? selectedUserIds 
      : routeUserIds || selectedUsers?.map(user => parseInt(user.id)) || [];
      
    if (userIds.length === 0) {
      errors.selectedUsers = `Please select at least one ${getRoleName(childRole)}`;
    }

    if (formData.applicabilityType === 'categories' && formData.selectedCategories.length === 0) {
      errors.selectedCategories = 'Please select at least one category';
    }

    if (formData.applicabilityType === 'products' && formData.selectedProducts.length === 0) {
      errors.selectedProducts = 'Please select at least one product';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle date change
  const handleDateChange = (event: any, selectedDate?: Date, type: 'start' | 'end' = 'start') => {
    if (Platform.OS === 'android') {
      setShowStartDatePicker(false);
      setShowEndDatePicker(false);
    }

    if (selectedDate) {
      if (type === 'start') {
        handleChange('startDate', selectedDate);
      } else {
        handleChange('endDate', selectedDate);
      }
    }
  };

  // Update the handleSubmit function to use selectedUserIds
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      console.log('🚀 [CREATE SCHEME] Submitting form data:', {
        formData,
        isEditMode,
        schemeId,
        currentUserId: currentUser?.id,
        selectedUserIds, // Use selectedUserIds instead of routeUserIds
        selectedUsers: selectedUsers?.map(u => ({ id: u.id, name: u.name }))
      });

      const description = formData.discountType === 'gift' ? formData.giftDescription :
                        formData.discountType === 'trip' ? formData.tripDescription : formData.description;

      // Use selectedUserIds as the primary source, fallback to routeUserIds or selectedUsers
      const userIds = selectedUserIds.length > 0 
        ? selectedUserIds 
        : routeUserIds || selectedUsers?.map(user => parseInt(user.id)) || [];

      // Prepare catalog IDs based on applicability type
      let catalogId: number[] = [];
      if (formData.applicabilityType === 'categories') {
        catalogId = formData.selectedCategories;
      } else if (formData.applicabilityType === 'products') {
        catalogId = formData.selectedProducts;
      }
      // For 'all', catalogId remains empty array

      console.log('📤 [CREATE SCHEME] Prepared payload data:', {
        userIds,
        catalogId,
        description,
        purchaseAmount: parseInt(formData.minQuantity) || 0,
        purchaseType: formData.purchaseType,
        applicabilityType: formData.applicabilityType
      });

      if (isEditMode) {
        const updatePayload: UpdateSchemeRequest = {
          userId: parseInt(currentUser?.id || '0'),
          description: description,
          startDate: formData.startDate.toISOString().split('T')[0],
          endDate: formData.endDate.toISOString().split('T')[0],
          offer: formData.discountType === 'gift' ? 'GIFT' as const : 'TRIP' as const,
          purchaseAmount: parseInt(formData.minQuantity) || 0,
          catalogId: catalogId,
          userIds: userIds,
          status: formData.status,
        };

        console.log('📤 [UPDATE SCHEME] Final API Payload:', updatePayload);

        await updateScheme({
          id: schemeId,
          data: updatePayload,
        }).unwrap();

        Alert.alert('Success', 'Scheme updated successfully!');
      } else {
        const createPayload: CreateSchemeRequest = {
          name: formData.name,
          description: description,
          startDate: formData.startDate.toISOString().split('T')[0],
          endDate: formData.endDate.toISOString().split('T')[0],
          offer: formData.discountType === 'gift' ? 'GIFT' as const : 'TRIP' as const,
          userId: parseInt(currentUser?.id || '0'),
          purchaseAmount: parseInt(formData.minQuantity) || 0,
          catalogId: catalogId,
          userIds: userIds,
        };

        console.log('📤 [CREATE SCHEME] Final API Payload:', createPayload);

        await createScheme(createPayload).unwrap();
        Alert.alert('Success', `Scheme created successfully for ${userIds.length} user${userIds.length > 1 ? 's' : ''}.`);
      }

      navigation.navigate('SchemeManagement');
    } catch (error: any) {
      console.error('❌ [CREATE SCHEME] Error saving scheme:', {
        error,
        errorData: error?.data,
        errorMessage: error?.message,
        isEditMode,
        schemeId
      });

      let errorMessage = 'Failed to save scheme. Please try again.';
      if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    }
  };

  // Render product item for selection
  const renderProductItem = ({ item }: { item: ProductItem }) => (
    <TouchableOpacity
      style={[
        styles.productItem,
        formData.selectedProducts.includes(item.id) && styles.productItemSelected
      ]}
      onPress={() => toggleProductSelection(item.id)}
    >
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name}</Text>
        <Text style={styles.productCode}>Code: {item.productCode}</Text>
        <Text style={styles.productDescription} numberOfLines={2}>
          {item.description}
        </Text>
      </View>
      <Icon
        name={formData.selectedProducts.includes(item.id) ? 'check-box' : 'check-box-outline-blank'}
        size={24}
        color={formData.selectedProducts.includes(item.id) ? '#6366f1' : '#9ca3af'}
      />
    </TouchableOpacity>
  );

  // Render bulk users info
  const renderBulkUsersInfo = () => {
    if (!isBulkMode || !selectedUsers) return null;

    const userIds = routeUserIds || selectedUsers.map(user => parseInt(user.id));

    return (
      <View style={styles.bulkUsersContainer}>
        <Text style={styles.bulkUsersTitle}>
          Creating scheme for  
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.userChipsContainer}>
          {selectedUsers.map((user: any) => (
            <View key={user.id} style={styles.userChip}>
              <Text style={styles.userChipText}>{user.name}</Text>
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Render image gallery
  const renderImageGallery = () => (
    <View style={styles.imageSection}>
      <Text style={styles.sectionTitle}>Scheme Images (Optional)</Text>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imageScrollView}>
        {/* Add Image Button */}
        <TouchableOpacity style={styles.addImageButton} onPress={handleImagePicker}>
          <Icon name="add-a-photo" size={32} color="#6366f1" />
          <Text style={styles.addImageText}>Add Image</Text>
        </TouchableOpacity>

        {/* Display selected images */}
        {formData.schemeImages.map((imageUri, index) => (
          <View key={index} style={styles.imageContainer}>
            <Image source={{ uri: imageUri }} style={styles.schemeImage} />
            <TouchableOpacity
              style={styles.removeImageButton}
              onPress={() => removeImage(index)}
            >
              <Icon name="close" size={20} color="white" />
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>

      {formData.schemeImages.length > 0 && (
        <Text style={styles.imageCountText}>
          {formData.schemeImages.length}/5 images selected
        </Text>
      )}
    </View>
  );

  // Add this render method for user item
  const renderUserItem = ({ item }: { item: User }) => (
    <TouchableOpacity
      style={[
        styles.userItem,
        selectedUserIds.includes(item.id) && styles.userItemSelected
      ]}
      onPress={() => toggleUserSelection(item.id)}
    >
      <View style={styles.userInfo}>
        <View style={styles.userHeader}>
          <Text style={styles.userName}>{item.firstName + '' + item.lastName}</Text>
          <View style={styles.userRoleBadge}>
            <Text style={styles.userRoleText}>{getRoleName(item.role)}</Text>
          </View>
        </View>
        <Text style={styles.userEmail}>{item.email}</Text>
        {item.parentId && (
          <Text style={styles.userParent}>Parent ID: {item.parentId}</Text>
        )}
      </View>
      <Icon
        name={selectedUserIds.includes(item.id) ? 'check-box' : 'check-box-outline-blank'}
        size={24}
        color={selectedUserIds.includes(item.id) ? '#6366f1' : '#9ca3af'}
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#6366f1" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{isEditMode ? 'Edit Scheme' : 'Create Scheme'}</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.formContainer} showsVerticalScrollIndicator={false}>
        {/* General Error Display */}
        {formErrors.general && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{formErrors.general}</Text>
          </View>
        )}

        {/* Bulk Users Info */}
        {renderBulkUsersInfo()}

        <Text style={styles.sectionTitle}>Scheme Details</Text>

        {/* Scheme Name */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Scheme Name</Text>
          <TextInput
            style={[styles.input, formErrors.name && styles.inputError]}
            placeholder="Enter scheme name"
            value={formData.name}
            onChangeText={(value) => handleChange('name', value)}
          />
          {formErrors.name && (
            <Text style={styles.errorText}>{formErrors.name}</Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>Discount Details</Text>

        {/* Discount Type */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Discount Type</Text>
          <View style={styles.radioGroupContainer}>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[
                  styles.radioButton,
                  formData.discountType === 'gift' && styles.radioButtonSelected
                ]}
                onPress={() => {
                  handleChange('discountType', 'gift');
                  setShowGiftModal(true);
                }}
              >
                <View style={styles.radioButtonInner}>
                  {formData.discountType === 'gift' && (
                    <View style={styles.radioButtonDot} />
                  )}
                </View>
                <Text style={styles.radioButtonText}>Gift</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.radioButton,
                  formData.discountType === 'trip' && styles.radioButtonSelected
                ]}
                onPress={() => {
                  handleChange('discountType', 'trip');
                  setShowTripModal(true);
                }}
              >
                <View style={styles.radioButtonInner}>
                  {formData.discountType === 'trip' && (
                    <View style={styles.radioButtonDot} />
                  )}
                </View>
                <Text style={styles.radioButtonText}>Trip</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Gift Description */}
        {formData.discountType === 'gift' && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Gift Description</Text>
            <View style={styles.discountInputContainer}>
              <TextInput
                style={[
                  styles.discountInput,
                  formErrors.giftDescription && styles.inputError
                ]}
                placeholder="Enter gift description"
                value={formData.giftDescription}
                onChangeText={(value) => handleChange('giftDescription', value)}
                multiline
                numberOfLines={3}
              />
              <TouchableOpacity
                style={styles.infoButton}
                onPress={() => setShowGiftModal(true)}
              >
                <Icon name="info-outline" size={20} color="#6366f1" />
              </TouchableOpacity>
            </View>
            {formErrors.giftDescription && (
              <Text style={styles.errorText}>{formErrors.giftDescription}</Text>
            )}
          </View>
        )}

        {/* Trip Description */}
        {formData.discountType === 'trip' && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Trip Description</Text>
            <View style={styles.discountInputContainer}>
              <TextInput
                style={[
                  styles.discountInput,
                  formErrors.tripDescription && styles.inputError
                ]}
                placeholder="Enter trip description"
                value={formData.tripDescription}
                onChangeText={(value) => handleChange('tripDescription', value)}
                multiline
                numberOfLines={3}
              />
              <TouchableOpacity
                style={styles.infoButton}
                onPress={() => setShowTripModal(true)}
              >
                <Icon name="info-outline" size={20} color="#6366f1" />
              </TouchableOpacity>
            </View>
            {formErrors.tripDescription && (
              <Text style={styles.errorText}>{formErrors.tripDescription}</Text>
            )}
          </View>
        )}

        <Text style={styles.sectionTitle}>Purchase Requirements</Text>

        {/* Purchase Type */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Purchase Type</Text>
          <View style={styles.radioGroupContainer}>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[
                  styles.radioButton,
                  formData.purchaseType === 'amount' && styles.radioButtonSelected
                ]}
                onPress={() => handleChange('purchaseType', 'amount')}
              >
                <View style={styles.radioButtonInner}>
                  {formData.purchaseType === 'amount' && (
                    <View style={styles.radioButtonDot} />
                  )}
                </View>
                <Text style={styles.radioButtonText}>Minimum Amount</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.radioButton,
                  formData.purchaseType === 'items' && styles.radioButtonSelected
                ]}
                onPress={() => handleChange('purchaseType', 'items')}
              >
                <View style={styles.radioButtonInner}>
                  {formData.purchaseType === 'items' && (
                    <View style={styles.radioButtonDot} />
                  )}
                </View>
                <Text style={styles.radioButtonText}>Minimum Items</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Minimum Purchase Value */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>
            {formData.purchaseType === 'amount' ? 'Minimum Purchase Amount (₹)' : 'Minimum Items Quantity'}
          </Text>
          <TextInput
            style={[
              styles.input,
              formErrors.minQuantity && styles.inputError
            ]}
            placeholder={formData.purchaseType === 'amount' ? 'Enter amount (e.g., 1000)' : 'Enter quantity (e.g., 5)'}
            value={formData.minQuantity}
            onChangeText={(value) => handleChange('minQuantity', value)}
            keyboardType="numeric"
          />
          {formErrors.minQuantity && (
            <Text style={styles.errorText}>{formErrors.minQuantity}</Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>Validity Period</Text>

        {/* Start Date */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Start Date</Text>
          <TouchableOpacity
            style={styles.dateInput}
            onPress={() => setShowStartDatePicker(true)}
          >
            <Text style={styles.dateText}>
              {formatDate(formData.startDate)}
            </Text>
            <Icon name="calendar-today" size={20} color="#6366f1" />
          </TouchableOpacity>
        </View>

        {/* End Date */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>End Date</Text>
          <TouchableOpacity
            style={[
              styles.dateInput,
              formErrors.endDate && styles.inputError
            ]}
            onPress={() => setShowEndDatePicker(true)}
          >
            <Text style={styles.dateText}>
              {formatDate(formData.endDate)}
            </Text>
            <Icon name="calendar-today" size={20} color="#6366f1" />
          </TouchableOpacity>
          {formErrors.endDate && (
            <Text style={styles.errorText}>{formErrors.endDate}</Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>Product Applicability</Text>

        <Text style={styles.sectionTitle}>User Selection</Text>

        {/* User Selection */}
        <View style={styles.inputGroup}>
          <View style={styles.selectionHeader}>
            <Text style={styles.inputLabel}>Select {getRoleName(childRole)}s</Text>
            <View style={styles.selectionActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={selectAllUsers}
              >
                <Text style={styles.actionButtonText}>Select All</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, styles.clearButton]}
                onPress={clearAllUsers}
              >
                <Text style={[styles.actionButtonText, styles.clearButtonText]}>Clear</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* User Selector Button */}
          <TouchableOpacity
            style={styles.userSelectorButton}
            onPress={() => setShowUserSelector(true)}
          >
            <View style={styles.userSelectorContent}>
              <View>
                <Text style={styles.userSelectorText}>
                  {selectedUserIds.length > 0
                    ? `${selectedUserIds.length} ${getRoleName(childRole)}${selectedUserIds.length > 1 ? 's' : ''} selected`
                    : `Select ${getRoleName(childRole)}s`}
                </Text>
                {selectedUserIds.length > 0 && (
                  <Text style={styles.userSelectorSubtext}>
                    Tap to view or modify selection
                  </Text>
                )}
              </View>
              <Icon name="arrow-forward-ios" size={16} color="#6366f1" />
            </View>
          </TouchableOpacity>

          {/* Selected Users Preview */}
          {selectedUserIds.length > 0 && (
            <View style={styles.selectedUsersPreview}>
              <Text style={styles.selectedUsersTitle}>Selected Users:</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false} 
                style={styles.selectedUsersScroll}
              >
                {getSelectedUsers().map((user: User) => (
                  <View key={user.id} style={styles.selectedUserChip}>
                    <Text style={styles.selectedUserChipText} numberOfLines={1}>
                      {user.firstName + '' + user.lastName}
                    </Text>
                    <TouchableOpacity
                      style={styles.removeUserButton}
                      onPress={() => toggleUserSelection(user.id)}
                    >
                      <Icon name="close" size={16} color="#6366f1" />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}

          {selectedUserIds.length > 0 && (
            <Text style={styles.selectionCount}>
              {selectedUserIds.length} user{selectedUserIds.length > 1 ? 's' : ''} selected
            </Text>
          )}
        </View>


        {/* Applicability Type Selection */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Apply scheme to:</Text>
          <View style={styles.applicabilityContainer}>
            <TouchableOpacity
              style={[
                styles.applicabilityOption,
                formData.applicabilityType === 'all' && styles.applicabilityOptionSelected
              ]}
              onPress={() => handleApplicabilityChange('all')}
            >
              <Icon 
                name="select-all" 
                size={24} 
                color={formData.applicabilityType === 'all' ? '#6366f1' : '#9ca3af'} 
              />
              <Text style={[
                styles.applicabilityText,
                formData.applicabilityType === 'all' && styles.applicabilityTextSelected
              ]}>
                All Products
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.applicabilityOption,
                formData.applicabilityType === 'categories' && styles.applicabilityOptionSelected
              ]}
              onPress={() => handleApplicabilityChange('categories')}
            >
              <Icon 
                name="category" 
                size={24} 
                color={formData.applicabilityType === 'categories' ? '#6366f1' : '#9ca3af'} 
              />
              <Text style={[
                styles.applicabilityText,
                formData.applicabilityType === 'categories' && styles.applicabilityTextSelected
              ]}>
                Specific Categories
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.applicabilityOption,
                formData.applicabilityType === 'products' && styles.applicabilityOptionSelected
              ]}
              onPress={() => handleApplicabilityChange('products')}
            >
              <Icon 
                name="inventory" 
                size={24} 
                color={formData.applicabilityType === 'products' ? '#6366f1' : '#9ca3af'} 
              />
              <Text style={[
                styles.applicabilityText,
                formData.applicabilityType === 'products' && styles.applicabilityTextSelected
              ]}>
                Specific Products
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Category Selection */}
        {formData.applicabilityType === 'categories' && (
          <View style={styles.inputGroup}>
            <View style={styles.selectionHeader}>
              <Text style={styles.inputLabel}>Select Categories</Text>
              <View style={styles.selectionActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={selectAllCategories}
                >
                  <Text style={styles.actionButtonText}>Select All</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, styles.clearButton]}
                  onPress={clearAllCategories}
                >
                  <Text style={[styles.actionButtonText, styles.clearButtonText]}>Clear</Text>
                </TouchableOpacity>
              </View>
            </View>

            {isCategoriesLoading ? (
              <ActivityIndicator size="small" color="#6366f1" style={{ marginVertical: 16 }} />
            ) : (
              <View style={styles.categoriesGrid}>
                {categoriesData.map((category: CategoryItem) => (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.categoryCard,
                      formData.selectedCategories.includes(category.id) && styles.categoryCardSelected
                    ]}
                    onPress={() => toggleCategorySelection(category.id)}
                  >
                    <Icon
                      name={formData.selectedCategories.includes(category.id) ? 'check-circle' : 'radio-button-unchecked'}
                      size={20}
                      color={formData.selectedCategories.includes(category.id) ? '#6366f1' : '#9ca3af'}
                    />
                    <Text style={[
                      styles.categoryCardText,
                      formData.selectedCategories.includes(category.id) && styles.categoryCardTextSelected
                    ]}>
                      {category.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {formData.selectedCategories.length > 0 && (
              <Text style={styles.selectionCount}>
                {formData.selectedCategories.length} categor{formData.selectedCategories.length > 1 ? 'ies' : 'y'} selected
              </Text>
            )}

            {formErrors.selectedCategories && (
              <Text style={styles.errorText}>{formErrors.selectedCategories}</Text>
            )}
          </View>
        )}

        {/* Product Selection */}
        {formData.applicabilityType === 'products' && (
          <View style={styles.inputGroup}>
            <View style={styles.selectionHeader}>
              <Text style={styles.inputLabel}>Select Products</Text>
              <View style={styles.selectionActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={selectAllProducts}
                >
                  <Text style={styles.actionButtonText}>Select All</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, styles.clearButton]}
                  onPress={clearAllProducts}
                >
                  <Text style={[styles.actionButtonText, styles.clearButtonText]}>Clear</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Category Filter for Products */}
            <View style={styles.categoryFilterContainer}>
              <Text style={styles.categoryFilterLabel}>Filter by categories (optional):</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryFilterScroll}>
                {categoriesData.map((category: CategoryItem) => (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.categoryFilterChip,
                      formData.selectedCategories.includes(category.id) && styles.categoryFilterChipSelected
                    ]}
                    onPress={() => toggleCategorySelection(category.id)}
                  >
                    <Text style={[
                      styles.categoryFilterChipText,
                      formData.selectedCategories.includes(category.id) && styles.categoryFilterChipTextSelected
                    ]}>
                      {category.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <TouchableOpacity
              style={styles.productSelectorButton}
              onPress={() => setShowProductSelector(true)}
            >
              <Text style={styles.productSelectorText}>
                {formData.selectedProducts.length > 0
                  ? `${formData.selectedProducts.length} products selected`
                  : 'Tap to select products'}
              </Text>
              <Icon name="arrow-forward-ios" size={16} color="#6366f1" />
            </TouchableOpacity>

            {formData.selectedProducts.length > 0 && (
              <Text style={styles.selectionCount}>
                {formData.selectedProducts.length} product{formData.selectedProducts.length > 1 ? 's' : ''} selected
              </Text>
            )}

            {formErrors.selectedProducts && (
              <Text style={styles.errorText}>{formErrors.selectedProducts}</Text>
            )}
          </View>
        )}

        {/* Image Gallery */}
        {renderImageGallery()}

        {/* Submit Button */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.submitButtonText}>
              {isEditMode ? 'Update Scheme' : 'Create Scheme'}
            </Text>
          )}
        </TouchableOpacity>

        {/* Information Note */}
        <View style={styles.noteContainer}>
          <Icon name="info" size={20} color="#6366f1" />
          <Text style={styles.noteText}>
            This scheme will be applicable to {getRoleName(childRole)}s only.
            {isBulkMode && ` It will be created for ${selectedUsers?.length} selected users.`}
          </Text>
        </View>

        <View style={{ height: 40 }} />
      </ScrollView>

      {/* Product Selector Modal */}
      <Modal
        visible={showProductSelector}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowProductSelector(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Products</Text>
              <TouchableOpacity
                onPress={() => setShowProductSelector(false)}
              >
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            {/* Product Selection Controls */}
            <View style={styles.productSelectionControls}>
              <Text style={styles.productCountText}>
                {formData.selectedProducts.length} of {getFilteredProducts().length} products selected
              </Text>
              <TouchableOpacity
                style={styles.selectAllModalButton}
                onPress={selectAllProducts}
              >
                <Text style={styles.selectAllModalText}>
                  {formData.selectedProducts.length === getFilteredProducts().length ? 'Deselect All' : 'Select All'}
                </Text>
              </TouchableOpacity>
            </View>

            {isAllProductsLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#6366f1" />
                <Text style={styles.loadingText}>Loading products...</Text>
              </View>
            ) : (
              <FlatList
                data={getFilteredProducts()}
                renderItem={renderProductItem}
                keyExtractor={item => item.id.toString()}
                contentContainerStyle={styles.productList}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={() => (
                  <View style={styles.emptyProductsContainer}>
                    <Icon name="inventory" size={48} color="#9ca3af" />
                    <Text style={styles.emptyProductsText}>
                      {formData.selectedCategories.length > 0 
                        ? 'No products found in selected categories'
                        : 'No products available'
                      }
                    </Text>
                  </View>
                )}
              />
            )}

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowProductSelector(false)}
              >
                <Text style={styles.modalButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Gift Description Modal */}
      <Modal
        visible={showGiftModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowGiftModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Gift Description</Text>
              <TouchableOpacity
                onPress={() => setShowGiftModal(false)}
              >
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.modalSubtitle}>Enter Gift Details</Text>
              <Text style={styles.modalDescription}>
                Describe the gift that will be offered as part of this scheme. Examples include:
              </Text>

              <View style={styles.exampleList}>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Television (32-inch LED TV)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Refrigerator (190L Double Door)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Air Conditioner (1.5 Ton Split AC)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="card-giftcard" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Smartphone (Latest Model)</Text>
                </View>
              </View>

              <TextInput
                style={[styles.input, styles.textArea, styles.modalInput]}
                placeholder="Enter detailed gift description"
                value={formData.giftDescription}
                onChangeText={(value) => handleChange('giftDescription', value)}
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowGiftModal(false)}
              >
                <Text style={styles.modalButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Trip Description Modal */}
      <Modal
        visible={showTripModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowTripModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Trip Description</Text>
              <TouchableOpacity
                onPress={() => setShowTripModal(false)}
              >
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.modalSubtitle}>Enter Trip Details</Text>
              <Text style={styles.modalDescription}>
                Describe the trip that will be offered as part of this scheme. Examples include:
              </Text>

              <View style={styles.exampleList}>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Maldives (5 days, 4 nights, all-inclusive)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Manali Trip (3 days, 2 nights, with activities)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Goa Beach Vacation (4 days, 3 nights)</Text>
                </View>
                <View style={styles.exampleItem}>
                  <Icon name="flight" size={20} color="#6366f1" />
                  <Text style={styles.exampleText}>Dubai Shopping Festival (7 days, 6 nights)</Text>
                </View>
              </View>

              <TextInput
                style={[styles.input, styles.textArea, styles.modalInput]}
                placeholder="Enter detailed trip description"
                value={formData.tripDescription}
                onChangeText={(value) => handleChange('tripDescription', value)}
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowTripModal(false)}
              >
                <Text style={styles.modalButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* User Selector Modal */}
      <Modal
        visible={showUserSelector}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowUserSelector(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select {getRoleName(childRole)}s</Text>
              <TouchableOpacity
                onPress={() => setShowUserSelector(false)}
              >
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>


            {/* User Selection Controls */}
            <View style={styles.userSelectionControls}>
              <Text style={styles.userCountText}>
                {selectedUserIds.length} of {getFilteredUsers().length} users selected
              </Text>
              <TouchableOpacity
                style={styles.selectAllModalButton}
                onPress={() => {
                  if (selectedUserIds.length === getFilteredUsers().length) {
                    clearAllUsers();
                  } else {
                    selectAllUsers();
                  }
                }}
              >
                <Text style={styles.selectAllModalText}>
                  {selectedUserIds.length === getFilteredUsers().length ? 'Deselect All' : 'Select All'}
                </Text>
              </TouchableOpacity>
            </View>

            {isUsersLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#6366f1" />
                <Text style={styles.loadingText}>Loading users...</Text>
              </View>
            ) : (
              <FlatList
                data={getFilteredUsers()}
                renderItem={renderUserItem}
                keyExtractor={item => item.id.toString()}
                contentContainerStyle={styles.userList}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={() => (
                  <View style={styles.emptyUsersContainer}>
                    <Icon name="people" size={48} color="#9ca3af" />
                    <Text style={styles.emptyUsersText}>
                      {searchQuery.trim() 
                        ? `No ${getRoleName(childRole)}s found matching "${searchQuery}"`
                        : `No ${getRoleName(childRole)}s available`
                      }
                    </Text>
                    {searchQuery.trim() && (
                      <TouchableOpacity
                        style={styles.clearSearchInEmptyButton}
                        onPress={() => setSearchQuery('')}
                      >
                        <Text style={styles.clearSearchInEmptyText}>Clear search</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              />
            )}

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalSecondaryButton]}
                onPress={() => {
                  clearAllUsers();
                  setShowUserSelector(false);
                }}
              >
                <Text style={styles.modalSecondaryButtonText}>Clear All</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowUserSelector(false)}
              >
                <Text style={styles.modalButtonText}>
                  Done ({selectedUserIds.length})
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>


      {/* Date Pickers */}
      {showStartDatePicker && (
        <DateTimePicker
          value={formData.startDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => handleDateChange(event, selectedDate, 'start')}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={formData.endDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => handleDateChange(event, selectedDate, 'end')}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  formContainer: {
    flex: 1,
    padding: 16,
  },
  bulkUsersContainer: {
    backgroundColor: '#eef2ff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#c7d2fe',
  },
  bulkUsersTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 4,
  },
  bulkUsersSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  userChipsContainer: {
    flexDirection: 'row',
  },
  userChip: {
    backgroundColor: '#ddd6fe',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginTop: 4,
  },
  errorContainer: {
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fecaca',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  radioGroupContainer: {
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  radioButtonSelected: {
    opacity: 1,
  },
  radioButtonInner: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#6366f1',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  radioButtonDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#6366f1',
  },
  radioButtonText: {
    fontSize: 16,
    color: '#4b5563',
  },
  discountInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  discountInput: {
    flex: 1,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  infoButton: {
    padding: 8,
    marginLeft: 8,
  },
  dateInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#1f2937',
  },
  applicabilityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  applicabilityOption: {
    flex: 1,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  applicabilityOptionSelected: {
    borderColor: '#6366f1',
    backgroundColor: '#f0f9ff',
  },
  applicabilityText: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
    textAlign: 'center',
  },
  applicabilityTextSelected: {
    color: '#6366f1',
    fontWeight: '500',
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  selectionActions: {
    flexDirection: 'row',
  },
  actionButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginLeft: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  clearButton: {
    backgroundColor: '#ef4444',
  },
  clearButtonText: {
    color: 'white',
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    margin: 4,
    minWidth: '45%',
  },
  categoryCardSelected: {
    borderColor: '#6366f1',
    backgroundColor: '#f0f9ff',
  },
  categoryCardText: {
    fontSize: 14,
    color: '#4b5563',
    marginLeft: 8,
    flex: 1,
  },
  categoryCardTextSelected: {
    color: '#6366f1',
    fontWeight: '500',
  },
  categoryFilterContainer: {
    marginBottom: 12,
  },
  categoryFilterLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  categoryFilterScroll: {
    marginBottom: 8,
  },
  categoryFilterChip: {
    backgroundColor: '#f3f4f6',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  categoryFilterChipSelected: {
    backgroundColor: '#6366f1',
    borderColor: '#6366f1',
  },
  categoryFilterChipText: {
    fontSize: 12,
    color: '#6b7280',
  },
  categoryFilterChipTextSelected: {
    color: 'white',
  },
  productSelectorButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productSelectorText: {
    fontSize: 16,
    color: '#1f2937',
  },
  selectionCount: {
    fontSize: 14,
    color: '#6366f1',
    marginTop: 8,
    fontWeight: '500',
  },
  imageSection: {
    marginTop: 16,
  },
  imageScrollView: {
    marginBottom: 8,
  },
  addImageButton: {
    width: 120,
    height: 120,
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#d1d5db',
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  addImageText: {
    fontSize: 12,
    color: '#6366f1',
    marginTop: 4,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  schemeImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ef4444',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageCountText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  submitButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 24,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  noteContainer: {
    flexDirection: 'row',
    backgroundColor: '#eef2ff',
    borderRadius: 8,
    padding: 12,
    marginTop: 24,
    alignItems: 'flex-start',
  },
  noteText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#4b5563',
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  modalBody: {
    padding: 16,
  },
  modalSubtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  modalDescription: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 16,
    lineHeight: 20,
  },
  exampleList: {
    marginBottom: 16,
  },
  exampleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  exampleText: {
    fontSize: 14,
    color: '#4b5563',
    marginLeft: 8,
  },
  modalInput: {
    marginTop: 8,
  },
  productSelectionControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  productCountText: {
    fontSize: 14,
    color: '#6b7280',
  },
  selectAllModalButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  selectAllModalText: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
  },
  productList: {
    padding: 16,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  productItemSelected: {
    backgroundColor: '#f3f4f6',
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  productCode: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  productDescription: {
    fontSize: 14,
    color: '#9ca3af',
    lineHeight: 18,
  },
  emptyProductsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyProductsText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  modalFooter: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    padding: 16,
    alignItems: 'flex-end',
  },
  modalButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  modalButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  // Add these styles to the existing styles object
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1f2937',
  },
  clearSearchButton: {
    padding: 4,
  },
  userSelectorButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 16,
  },
  userSelectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userSelectorText: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
  },
  userSelectorSubtext: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  selectedUsersPreview: {
    marginTop: 12,
  },
  selectedUsersTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 8,
  },
  selectedUsersScroll: {
    marginBottom: 8,
  },
  selectedUserChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eef2ff',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    maxWidth: 150,
  },
  selectedUserChipText: {
    fontSize: 12,
    color: '#6366f1',
    fontWeight: '500',
    flex: 1,
  },
  removeUserButton: {
    marginLeft: 6,
    padding: 2,
  },
  modalSearchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  userSelectionControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  userCountText: {
    fontSize: 14,
    color: '#6b7280',
  },
  userList: {
    padding: 16,
  },
  userItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  userItemSelected: {
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
  },
  userInfo: {
    flex: 1,
    marginRight: 12,
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
    userName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    flex: 1,
  },
  userRoleBadge: {
    backgroundColor: '#ddd6fe',
    borderRadius: 12,
    paddingVertical: 2,
    paddingHorizontal: 8,
  },
  userRoleText: {
    fontSize: 12,
    color: '#6366f1',
    fontWeight: '500',
  },
  userEmail: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  userParent: {
    fontSize: 12,
    color: '#9ca3af',
  },
  emptyUsersContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyUsersText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  clearSearchInEmptyButton: {
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#f3f4f6',
    borderRadius: 6,
  },
  clearSearchInEmptyText: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '500',
  },
  modalSecondaryButton: {
    backgroundColor: '#f3f4f6',
    marginRight: 12,
  },
  modalSecondaryButtonText: {
    color: '#6b7280',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default CreateSchemeScreen;


