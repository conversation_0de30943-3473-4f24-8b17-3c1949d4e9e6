import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
  Image,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  FAB,
  useTheme,
  IconButton,
  Surface,
  Card,
  Button,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import { useGetOffersQuery } from '../../components/Home/api/Promotionalslice';

interface Offer {
  id: number;
  title: string;
  imageUrl: string;
  region: string;
  state: string;
  city: string;
  area: string;
  startDate: string;
  endDate: string;
  status?: 'active' | 'inactive';
  createdBy?: string;
  description?: string;
}

interface OfferCardProps {
  offer: Offer;
  onViewDetails: (offer: Offer) => void;
  onEdit?: (offer: Offer) => void;
  onDelete?: (offer: Offer) => void;
  canEdit: boolean;
}

const OfferCard: React.FC<OfferCardProps> = ({
  offer,
  onViewDetails,
  onEdit,
  onDelete,
  canEdit,
}) => {
  const theme = useTheme();

  // Format date for display
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card style={styles.offerCard}>
      <View style={styles.offerImageContainer}>
        <Image
          source={{ uri: offer.imageUrl }}
          style={styles.offerImage}
          resizeMode="cover"
        />
        <View style={styles.offerImageOverlay}>
          <Chip
            mode="flat"
            style={[
              styles.statusChip,
              {
                backgroundColor: offer.status === 'active' ? '#10b98120' : '#ef444420'
              }
            ]}
          >
            <Text
              style={{
                color: offer.status === 'active' ? '#10b981' : '#ef4444',
                fontWeight: '500'
              }}
            >
              {offer.status === 'active' ? 'Active' : 'Inactive'}
            </Text>
          </Chip>
        </View>
      </View>

      <Card.Content>
        <View style={styles.offerHeader}>
          <Text variant="titleMedium" style={styles.offerTitle}>{offer.title}</Text>
        </View>

        <View style={styles.offerDetailsContainer}>
          <View style={styles.offerDetailRow}>
            <Icon name="location-on" size={16} color={theme.colors.primary} style={styles.offerDetailIcon} />
            <Text style={styles.offerDetailText}>{offer.region}, {offer.state}</Text>
          </View>

          <View style={styles.offerDetailRow}>
            <Icon name="location-city" size={16} color="#6b7280" style={styles.offerDetailIcon} />
            <Text style={styles.offerDetailText}>{offer.city}, {offer.area}</Text>
          </View>

          <View style={styles.offerDetailRow}>
            <Icon name="calendar-today" size={16} color="#6b7280" style={styles.offerDetailIcon} />
            <Text style={styles.offerDetailText}>
              Valid: {formatDate(offer.startDate)} to {formatDate(offer.endDate)}
            </Text>
          </View>

          {offer.createdBy && (
            <View style={styles.offerDetailRow}>
              <Icon name="person" size={16} color="#6b7280" style={styles.offerDetailIcon} />
              <Text style={styles.offerDetailText}>Created by: {offer.createdBy}</Text>
            </View>
          )}
        </View>
      </Card.Content>

      <Card.Actions style={styles.offerActions}>
        <Button
          mode="text"
          onPress={() => onViewDetails(offer)}
          icon={({size, color}) => (
            <Icon name="visibility" size={size} color={color} />
          )}
          textColor={theme.colors.primary}
        >
          View Details
        </Button>

        {canEdit && onEdit && (
          <Button
            mode="text"
            onPress={() => onEdit(offer)}
            icon={({size, color}) => (
              <Icon name="edit" size={size} color={color} />
            )}
            textColor="#0284C7"
          >
            Edit
          </Button>
        )}

        {canEdit && onDelete && (
          <Button
            mode="text"
            onPress={() => onDelete(offer)}
            icon={({size, color}) => (
              <Icon name="delete" size={size} color={color} />
            )}
            textColor="#ef4444"
          >
            Delete
          </Button>
        )}
      </Card.Actions>
    </Card>
  );
};

type RootStackParamList = {
  OffersManagement: { userId?: string; userName?: string; userRole?: UserRole };
  CreateOffer: { parentId?: string; childRole: UserRole };
  EditOffer: { offerId: number };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'OffersManagement'>;
type OffersManagementRouteProp = RouteProp<RootStackParamList, 'OffersManagement'>;

const OffersManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<OffersManagementRouteProp>();
  const { currentUser } = useUser();
  const theme = useTheme();

  // Extract route params
  const { userId, userName, userRole } = route.params || {};

  const [offers, setOffers] = useState<Offer[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // API hook to fetch offers
  const { data: apiResponse, isLoading, error, refetch } = useGetOffersQuery({
    status: 1,
    page: 0,
    size: 50,
  });

  // Get role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  // Get the applicable child role based on current user's role
  const getApplicableChildRole = (): UserRole | null => {
    if (!currentUser) return null;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return null;
    }
  };

  // Process offers data
  useEffect(() => {
    if (apiResponse) {
      let processedOffers: Offer[] = apiResponse.map((offer: any) => ({
        ...offer,
        status: 'active' as const, // Default status since API doesn't provide it
        createdBy: 'System', // Default creator since API doesn't provide it
        description: offer.title, // Use title as description if not provided
      }));

      // Apply search filter
      if (searchQuery) {
        processedOffers = processedOffers.filter(offer =>
          offer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          offer.region.toLowerCase().includes(searchQuery.toLowerCase()) ||
          offer.state.toLowerCase().includes(searchQuery.toLowerCase()) ||
          offer.city.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply status filter
      if (selectedStatus && selectedStatus !== 'all') {
        processedOffers = processedOffers.filter(offer => offer.status === selectedStatus);
      }

      setOffers(processedOffers);
    }
  }, [apiResponse, searchQuery, selectedStatus]);

  // Handle API errors
  useEffect(() => {
    if (error) {
      console.error('Error loading offers from API:', error);
      Alert.alert('Error', 'Failed to load offers. Please try again.');
    }
  }, [error]);

  // Check permissions
  const canEditOffer = (offer: Offer): boolean => {
    if (!currentUser) return false;

    // Ooge Team can edit all offers
    if (currentUser.role === UserRole.OOGE_TEAM || currentUser.role === UserRole.SUPER_STOCKIST || currentUser.role === UserRole.DISTRIBUTOR) return true;

    // Others can only edit offers they created
    return offer.createdBy === currentUser.name;
  };

  const canDeleteOffer = (offer: Offer): boolean => {
    return canEditOffer(offer);
  };

  // Check if user can create offers
  const canCreateOffer = (): boolean => {
    if (!currentUser) return false;

    // Retailers cannot create offers
    if (currentUser.role === UserRole.RETAILER || currentUser.role === UserRole.PUBLIC) {
      return false;
    }

    return true;
  };

  // Navigate to create offer screen
  const handleCreateOffer = () => {
    const childRole = getApplicableChildRole();

    if (!childRole) {
      Alert.alert('Error', 'You do not have permission to create offers');
      return;
    }

    navigation.navigate('CreateOffer', {
      parentId: currentUser?.id,
      childRole,
    });
  };

  // Handle view details for an offer
  const handleViewOfferDetails = (offer: Offer) => {
    Alert.alert(
      'Offer Details',
      `Title: ${offer.title}\n\nLocation: ${offer.region}, ${offer.state}, ${offer.city}, ${offer.area}\n\nValid From: ${offer.startDate}\nValid To: ${offer.endDate}\n\nStatus: ${offer.status}\nCreated By: ${offer.createdBy}`,
      [{ text: 'OK' }]
    );
  };

  // Handle edit for an offer
  const handleEditOffer = (offer: Offer) => {
    if (!canEditOffer(offer)) {
      Alert.alert('Permission Denied', 'You do not have permission to edit this offer.');
      return;
    }
    navigation.navigate('EditOffer', { offerId: offer.id });
  };

  // Handle delete for an offer
  const handleDeleteOffer = (offer: Offer) => {
    if (!canDeleteOffer(offer)) {
      Alert.alert('Permission Denied', 'You do not have permission to delete this offer.');
      return;
    }

    Alert.alert(
      'Delete Offer',
      `Are you sure you want to delete "${offer.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setOffers(prev => prev.filter(o => o.id !== offer.id));
            Alert.alert('Success', 'Offer deleted successfully.');
          },
        },
      ]
    );
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    canCreateOffer() && (
      <IconButton
        icon="plus"
        iconColor="white"
        size={24}
        onPress={handleCreateOffer}
      />
    )
  );

  // Status filter options
  const statusOptions = [
    { id: 'all', label: 'All Status' },
    { id: 'active', label: 'Active' },
    { id: 'inactive', label: 'Inactive' }
  ];

  // Handle status filter change
  const handleStatusFilterChange = (statusId: string) => {
    setSelectedStatus(statusId === 'all' ? null : statusId);
  };

  // Render content
  const renderContent = () => {
    if (offers.length === 0 && !isLoading) {
      return (
        <EmptyState
          icon="card-giftcard"
          message={searchQuery || selectedStatus ?
            'No offers match your filters' : 'No offers found'}
          actionLabel={canCreateOffer() ? 'Create New Offer' : undefined}
          onAction={canCreateOffer() ? handleCreateOffer : undefined}
        />
      );
    }

    return (
      <FlatList
        data={offers}
        renderItem={({ item }) => (
          <OfferCard
            offer={item}
            onViewDetails={handleViewOfferDetails}
            onEdit={handleEditOffer}
            onDelete={handleDeleteOffer}
            canEdit={canEditOffer(item)}
          />
        )}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.offersList}
        showsVerticalScrollIndicator={false}
        refreshing={isLoading}
        onRefresh={refetch}
      />
    );
  };

  return (
    <BaseManagementScreen
      title="Offers Management"
      showBack={true}
      rightActions={renderHeaderRightActions()}
      subtitle={userName ? `Managing offers for ${userName}` : 'Global offers management'}
      isLoading={isLoading}
      loadingText="Loading offers..."
    >
      {/* User Info (if provided) */}
      {userName && userRole && (
        <Surface style={styles.userInfoContainer} elevation={1}>
          <Text variant="titleMedium" style={styles.userName}>{userName}</Text>
          <Text variant="bodyMedium" style={styles.userRole}>{getRoleName(userRole)}</Text>
        </Surface>
      )}

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search offers..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FilterChips
          options={statusOptions}
          selectedId={selectedStatus || 'all'}
          onSelect={handleStatusFilterChange}
        />
      </View>

      {renderContent()}

      {canCreateOffer() && (
        <FAB
          icon="plus"
          style={[styles.fab, { backgroundColor: theme.colors.primary }]}
          onPress={handleCreateOffer}
          color="white"
        />
      )}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  userInfoContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  userName: {
    fontWeight: 'bold',
  },
  userRole: {
    color: '#6b7280',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 8,
  },
  offersList: {
    padding: 16,
    paddingBottom: 80,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  offerCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
    overflow: 'hidden',
  },
  offerImageContainer: {
    position: 'relative',
    height: 200,
  },
  offerImage: {
    width: '100%',
    height: '100%',
  },
  offerImageOverlay: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
  statusChip: {
    borderRadius: 12,
    height: 28,
  },
  offerHeader: {
    marginBottom: 12,
  },
  offerTitle: {
    fontWeight: 'bold',
  },
  offerDetailsContainer: {
    marginBottom: 8,
  },
  offerDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  offerDetailIcon: {
    marginRight: 8,
  },
  offerDetailText: {
    color: '#4b5563',
    fontSize: 14,
    flex: 1,
  },
  offerActions: {
    justifyContent: 'flex-start',
  },
});

export default OffersManagementScreen;
