import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  useTheme,
  FAB,
  Surface,
  IconButton,
} from 'react-native-paper';

import { useUser } from '../../context/UserContext';
import { UserRole, User } from '../../data/mockData';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import {
  PricingRule,
} from './api/price';

type RootStackParamList = {
  PricingList: { userId?: string; userName?: string; userRole?: UserRole };
  CreatePricing: { parentId?: string; childRole: UserRole };
  PricingManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: User[]; pricingRule?: PricingRule };
  EditPricing: { pricingId: number };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'PricingList'>;
type PricingListRouteProp = RouteProp<RootStackParamList, 'PricingList'>;

interface PricingCard {
  pricing: PricingRule;
  onViewDetails: (pricing: PricingRule) => void;
  onEdit?: (pricing: PricingRule) => void;
  onDelete?: (pricing: PricingRule) => void;
  canEdit: boolean;
}

const PricingCardComponent: React.FC<PricingCard> = ({
  pricing,
  onViewDetails,
  onEdit,
  onDelete,
  canEdit,
}) => {
  const theme = useTheme();

  const getStatusColor = (status: number) => {
    return status === 1 ? '#10b981' : '#ef4444';
  };

  const getStatusText = (status: number) => {
    return status === 1 ? 'Active' : 'Inactive';
  };

  const getUserCount = () => {
    return pricing.isGlobal ? 'All Users' : `${pricing.userIds.length} Users`;
  };

  const getProductCount = () => {
    return pricing.catalogIds.length === 0 ? 'All Products' : `${pricing.catalogIds.length} Products`;
  };

  return (
    <Surface style={styles.card} elevation={2}>
      <View style={styles.cardHeader}>
        <View style={styles.cardHeaderLeft}>
          <Text variant="titleMedium" style={styles.pricingName}>
            {pricing.name}
          </Text>
          <Text variant="bodySmall" style={styles.pricingType}>
            {pricing.type} • {pricing.discountPercentage}% Discount
          </Text>
        </View>
        <View style={[styles.statusChip, { backgroundColor: getStatusColor(pricing.status) }]}>
          <Text style={styles.statusText}>{getStatusText(pricing.status)}</Text>
        </View>
      </View>

      <Text variant="bodyMedium" style={styles.pricingDescription} numberOfLines={2}>
        {pricing.description}
      </Text>

      <View style={styles.pricingStats}>
        <View style={styles.statItem}>
          <Text variant="bodySmall" style={styles.statLabel}>Users</Text>
          <Text variant="bodyMedium" style={styles.statValue}>{getUserCount()}</Text>
        </View>
        <View style={styles.statItem}>
          <Text variant="bodySmall" style={styles.statLabel}>Products</Text>
          <Text variant="bodyMedium" style={styles.statValue}>{getProductCount()}</Text>
        </View>
        <View style={styles.statItem}>
          <Text variant="bodySmall" style={styles.statLabel}>Type</Text>
          <Text variant="bodyMedium" style={styles.statValue}>
            {pricing.isGlobal ? 'Global' : 'Individual'}
          </Text>
        </View>
      </View>

      <View style={styles.cardActions}>
        <IconButton
          icon="eye"
          size={20}
          onPress={() => onViewDetails(pricing)}
        />
        {canEdit && onEdit && (
          <IconButton
            icon="pencil"
            size={20}
            onPress={() => onEdit(pricing)}
          />
        )}
        {canEdit && onDelete && (
          <IconButton
            icon="delete"
            size={20}
            onPress={() => onDelete(pricing)}
          />
        )}
      </View>
    </Surface>
  );
};

const PricingListScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<PricingListRouteProp>();
  const { currentUser } = useUser();
  const theme = useTheme();

  // Extract route params
  const { userId, userName, userRole } = route.params || {};

  const [pricingRules, setPricingRules] = useState<PricingRule[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // API hooks - Mock data for now since the API endpoint doesn't exist yet
  const currentUserId = currentUser?.id ? parseInt(currentUser.id) : 0;

  // Placeholder variables since the getPricingRules endpoint has been removed.
  // In the future, replace this with the appropriate pricing query once available.
  const [pricingData] = useState<{ data?: PricingRule[] }>({});
  const isLoadingPricing = false;
  const refetch = () => {};

  // Get role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  // Get the applicable child role based on current user's role
  const getApplicableChildRole = (): UserRole | null => {
    if (!currentUser) return null;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return null;
    }
  };

  // Mock data for development - Remove when real API is available
  useEffect(() => {
    if (!pricingData?.data) {
      // Mock pricing rules data
      const mockPricingRules: PricingRule[] = [
        {
          id: 1,
          name: 'Summer Sale Distributor Pricing',
          description: 'Special pricing for distributors during summer sale period',
          type: 'DISTRIBUTOR',
          userIds: [1, 2, 3],
          catalogIds: ['101', '102', '103'],
          discountPercentage: 15,
          isGlobal: false,
          status: 1,
          createdBy: currentUserId,
          updatedBy: currentUserId,
          createdAt: '2025-01-01T00:00:00Z',
          updatedAt: '2025-01-15T00:00:00Z',
        },
        {
          id: 2,
          name: 'All Retailers - Electronics Discount',
          description: 'Global pricing for all retailers on electronics category',
          type: 'RETAILER',
          userIds: [],
          catalogIds: ['201', '202'],
          discountPercentage: 10,
          isGlobal: true,
          status: 1,
          createdBy: currentUserId,
          updatedBy: currentUserId,
          createdAt: '2025-01-10T00:00:00Z',
          updatedAt: '2025-01-10T00:00:00Z',
        },
        {
          id: 3,
          name: 'Bulk Purchase Incentive',
          description: 'Special pricing for high-volume distributors',
          type: 'DISTRIBUTOR',
          userIds: [4, 5],
          catalogIds: [],
          discountPercentage: 20,
          isGlobal: false,
          status: 0,
          createdBy: currentUserId,
          updatedBy: currentUserId,
          createdAt: '2024-12-01T00:00:00Z',
          updatedAt: '2024-12-15T00:00:00Z',
        },
      ];
      setPricingRules(mockPricingRules);
    }
  }, [pricingData, currentUserId]);

  // Process pricing rules data
  useEffect(() => {
    console.log('📊 [PRICING LIST] Processing pricing data:', {
      pricingData,
      searchQuery,
      selectedStatus,
      selectedType,
      currentUserId
    });

    if (pricingData?.data) {
      let filteredPricing = [...pricingData.data];

      // Apply search filter
      if (searchQuery) {
        filteredPricing = filteredPricing.filter(pricing =>
          pricing.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          pricing.description.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply status filter
      if (selectedStatus !== null) {
        filteredPricing = filteredPricing.filter(pricing => pricing.status === selectedStatus);
      }

      // Apply type filter
      if (selectedType !== null) {
        filteredPricing = filteredPricing.filter(pricing => pricing.type === selectedType);
      }

      setPricingRules(filteredPricing);
    }
  }, [pricingData, searchQuery, selectedStatus, selectedType, currentUserId]);

  // Refresh pricing rules
  const refreshPricingRules = () => {
    refetch();
  };

  // Check permissions
  const canEditPricing = (pricing: PricingRule): boolean => {
    if (!currentUser) return false;

    // Ooge Team and higher roles can edit all pricing
    if ([UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role)) {
      return true;
    }

    // Others can only edit pricing they created
    return pricing.createdBy === parseInt(currentUser.id);
  };

  const canDeletePricing = (pricing: PricingRule): boolean => {
    return canEditPricing(pricing);
  };

  // Check if user can create pricing
  const canCreatePricing = (): boolean => {
    if (!currentUser) return false;

    // Retailers cannot create pricing
    if (currentUser.role === UserRole.RETAILER || currentUser.role === UserRole.PUBLIC) {
      return false;
    }

    return true;
  };

  // Navigate to create pricing screen
  const handleCreatePricing = () => {
    const childRole = getApplicableChildRole();

    if (!childRole) {
      Alert.alert('Error', 'You do not have permission to create pricing');
      return;
    }

    navigation.navigate('CreatePricing', {
      parentId: currentUser?.id,
      childRole,
    });
  };

  // Handle view details for a pricing rule
  const handleViewPricingDetails = (pricing: PricingRule) => {
    Alert.alert('Pricing Details', `Viewing details for pricing: ${pricing.name}`);
    // In a real app, navigate to a detail screen
  };

  // Handle edit for a pricing rule
  const handleEditPricing = (pricing: PricingRule) => {
    if (!canEditPricing(pricing)) {
      Alert.alert('Permission Denied', 'You do not have permission to edit this pricing.');
      return;
    }
    navigation.navigate('PricingManagement', { 
      userId: currentUser?.id,
      userName: currentUser?.name,
      userRole: currentUser?.role,
      pricingRule: pricing
    });
  };

  // Handle delete for a pricing rule
  const handleDeletePricing = (pricing: PricingRule) => {
    if (!canDeletePricing(pricing)) {
      Alert.alert('Permission Denied', 'You do not have permission to delete this pricing.');
      return;
    }

    Alert.alert(
      'Delete Pricing',
      `Are you sure you want to delete "${pricing.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // For now, just show a message since delete API is not available
            Alert.alert('Info', 'Delete functionality will be implemented when the API is available.');
          },
        },
      ]
    );
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    canCreatePricing() && (
      <IconButton
        icon="plus"
        iconColor="white"
        size={24}
        onPress={handleCreatePricing}
      />
    )
  );

  // Status filter options
  const statusOptions = [
    { id: 'all', label: 'All Status' },
    { id: '1', label: 'Active' },
    { id: '0', label: 'Inactive' }
  ];

  // Type filter options
  const typeOptions = [
    { id: 'all', label: 'All Types' },
    { id: 'DISTRIBUTOR', label: 'Distributor' },
    { id: 'RETAILER', label: 'Retailer' }
  ];

  // Handle status filter change
  const handleStatusFilterChange = (statusId: string) => {
    setSelectedStatus(statusId === 'all' ? null : parseInt(statusId));
  };

  // Handle type filter change
  const handleTypeFilterChange = (typeId: string) => {
    setSelectedType(typeId === 'all' ? null : typeId);
  };

  // Render content
  const renderContent = () => {
    if (pricingRules.length === 0 && !isLoading) {
      return (
        <EmptyState
          icon="attach-money"
          message={searchQuery || selectedStatus !== null || selectedType !== null ?
            'No pricing rules match your filters' : 'No pricing rules found'}
          actionLabel={canCreatePricing() ? 'Create New Pricing' : undefined}
          onAction={canCreatePricing() ? handleCreatePricing : undefined}
        />
      );
    }

    return (
      <FlatList
        data={pricingRules}
        renderItem={({ item }) => (
          <PricingCardComponent
            pricing={item}
            onViewDetails={handleViewPricingDetails}
            onEdit={handleEditPricing}
            onDelete={handleDeletePricing}
            canEdit={canEditPricing(item)}
          />
        )}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.pricingList}
        showsVerticalScrollIndicator={false}
        refreshing={isLoadingPricing}
        onRefresh={refreshPricingRules}
      />
    );
  };

  return (
    <BaseManagementScreen
      title="Pricing Management"
      showBack={true}
      rightActions={renderHeaderRightActions()}
      subtitle={userName ? `Managing pricing for ${userName}` : 'Global pricing management'}
      isLoading={isLoading}
      loadingText="Loading pricing rules..."
    >
      {/* User Info (if provided) */}
      {userName && userRole && (
        <Surface style={styles.userInfoContainer} elevation={1}>
          <Text variant="titleMedium" style={styles.userName}>{userName}</Text>
          <Text variant="bodyMedium" style={styles.userRole}>{getRoleName(userRole)}</Text>
        </Surface>
      )}

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search pricing rules..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FilterChips
          options={statusOptions}
          selectedId={selectedStatus?.toString() || 'all'}
          onSelect={handleStatusFilterChange}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FilterChips
          options={typeOptions}
          selectedId={selectedType || 'all'}
          onSelect={handleTypeFilterChange}
        />
      </View>

      {renderContent()}

      {canCreatePricing() && (
        <FAB
          icon="plus"
          style={[styles.fab, { backgroundColor: theme.colors.primary }]}
          onPress={handleCreatePricing}
          color="white"
        />
      )}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  userInfoContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  userName: {
    fontWeight: 'bold',
  },
  userRole: {
    color: '#6b7280',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 8,
  },
  pricingList: {
    padding: 16,
    paddingBottom: 80,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  card: {
    marginBottom: 12,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  cardHeaderLeft: {
    flex: 1,
    marginRight: 12,
  },
  pricingName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  pricingType: {
    color: '#6b7280',
    fontSize: 12,
  },
  statusChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  pricingDescription: {
    marginBottom: 12,
    color: '#6b7280',
  },
  pricingStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    color: '#6b7280',
    marginBottom: 4,
  },
  statValue: {
    fontWeight: 'bold',
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 12,
    marginTop: 12,
  },
});

export default PricingListScreen;
