import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Animated,
  Alert,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  TextInput,
  Surface,
  IconButton,
  Chip,
} from 'react-native-paper';
import { useUser } from '../../context/UserContext';
import { UserRole, User } from '../../data/mockData';
import EnhancedPricingCard from '../../components/management/EnhancedPricingCard';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import {
  useGetCategoriesQuery,
  useGetProductsQuery,
  useGetProductsByCategoryQuery,
} from '../../services/api/apiSlice';
import {
  useCreateDistributorPriceMutation,
  useCreateRetailerPriceMutation,
  DistributorPriceRequest,
  RetailerPriceRequest,
} from './api/price';

type RootStackParamList = {
  PricingManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: User[] };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'PricingManagement'>;
type PricingManagementRouteProp = RouteProp<RootStackParamList, 'PricingManagement'>;

interface PricingItem {
  productId: string;
  productName: string;
  productCode?: string;
  description?: string;
  basePrice: number;
  margin: number;
  finalPrice: number;
  isCustomPrice: boolean;
  category: string;
}

const PricingManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<PricingManagementRouteProp>();
  const { currentUser } = useUser();
  const theme = useTheme();

  // State declarations
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  const {
    data: categoriesData,
    isLoading: isCategoriesLoading,
    error: categoriesError,
    refetch: refetchCategories
  } = useGetCategoriesQuery({ page: 0, count: 50, type: 1, status: 1 });

  const {
    data: productsData,
    isLoading: isProductsLoading,
    error: productsError,
    refetch: refetchProducts
  } = useGetProductsQuery({ page: 0, count: 100, type: 2, status: 1 }, {
    skip: selectedCategory !== null && selectedCategory !== 'All'
  });

  const {
    data: categoryProductsData,
    isLoading: isCategoryProductsLoading,
    error: categoryProductsError,
    refetch: refetchCategoryProducts
  } = useGetProductsByCategoryQuery(
    categoriesData?.find((cat: any) => cat.name === selectedCategory)?.id,
    {
      skip: !selectedCategory || selectedCategory === 'All' || !categoriesData
    }
  );

  // Extract route params
  const { userName, userRole, userId, selectedUsers } = route.params || {};

  // Determine if this is bulk mode
  const isBulkMode = selectedUsers && selectedUsers.length > 0;

  // Set display name based on mode
  const displayName = isBulkMode
    ? `${selectedUsers.length} Users Selected`
    : userName || 'All Users';

  // Tab navigation state
  const [activeTab, setActiveTab] = useState<'individual' | 'global'>('individual');
  const tabPosition = useState(new Animated.Value(0))[0];

  const [isLoading, setIsLoading] = useState(true);
  const [pricingItems, setPricingItems] = useState<PricingItem[]>([]);
  const [globalPricingItems, setGlobalPricingItems] = useState<PricingItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [globalMargin, setGlobalMargin] = useState('');
  const [applyingGlobalMargin, setApplyingGlobalMargin] = useState(false);
  const [globalMarkup, setGlobalMarkup] = useState('');

  // Pricing API hooks
  const [createDistributorPrice, { isLoading: isCreatingDistributorPrice }] = useCreateDistributorPriceMutation();
  const [createRetailerPrice, { isLoading: isCreatingRetailerPrice }] = useCreateRetailerPriceMutation();

  // Switch between tabs
  const switchTab = (tab: 'individual' | 'global') => {
    Animated.spring(tabPosition, {
      toValue: tab === 'individual' ? 0 : 1,
      useNativeDriver: false,
      friction: 8,
      tension: 70
    }).start();
    setActiveTab(tab);
  };

  // Extract unique categories from products
  useEffect(() => {
    const isLoadingData = isCategoriesLoading || isProductsLoading ||
      (!!selectedCategory && selectedCategory !== 'All' && isCategoryProductsLoading);
    setIsLoading(isLoadingData);
  }, [isCategoriesLoading, isProductsLoading, isCategoryProductsLoading, selectedCategory]);

  useEffect(() => {
    if (categoriesData) {
      const uniqueCategories = Array.from(
        new Set(categoriesData.map((cat: any) => cat.name))
      ).filter(Boolean) as string[];

      setCategories(uniqueCategories);
    }
  }, [categoriesData]);

  // Combined effect to handle both all products and category-specific products
  useEffect(() => {
    const currentProductsData = selectedCategory && selectedCategory !== 'All'
      ? categoryProductsData
      : productsData;

    if (!currentProductsData) return;

    console.log('📊 [PRICING MANAGEMENT] Loading products:', {
      selectedCategory,
      dataSource: selectedCategory && selectedCategory !== 'All' ? 'category' : 'all',
      productCount: currentProductsData.length
    });

    // Transform API products data to PricingItem format
    const items: PricingItem[] = currentProductsData.map((product: any) => {
      const defaultMargin = 10; // Default discount percentage
      const basePrice = product.basePrice || 1000;
      const finalPrice = basePrice * (1 - defaultMargin / 100); // Apply discount

      return {
        productId: product.id.toString(),
        productName: product.name,
        productCode: product.productCode,
        description: product.description,
        basePrice: basePrice,
        margin: defaultMargin,
        finalPrice: finalPrice,
        isCustomPrice: false,
        category: selectedCategory || 'All',
      };
    });

    console.log('📊 [PRICING MANAGEMENT] Processed pricing items:', {
      itemCount: items.length,
      sampleItem: items[0]
    });

    setPricingItems(items);
    setGlobalPricingItems([...items]);
  }, [productsData, categoryProductsData, selectedCategory]);

  // Get the active pricing items based on the current tab
  const getActivePricingItems = () => {
    return activeTab === 'individual' ? pricingItems : globalPricingItems;
  };

  // Filter pricing items based on search query and category
  const getFilteredItems = () => {
    const activeItems = getActivePricingItems();

    return activeItems.filter(item => {
      const matchesSearch = !searchQuery ||
        item.productName.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesCategory = !selectedCategory || item.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  };

  const filteredItems = getFilteredItems();

  // Handle margin change for individual items
  const handleMarginChange = (productId: string, margin: number) => {
    if (isNaN(margin) || margin < 0) return;

    console.log('📊 [PRICING MANAGEMENT] Updating margin:', { productId, margin });

    const updateItems = activeTab === 'individual' ? setPricingItems : setGlobalPricingItems;

    updateItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          const finalPrice = item.basePrice * (1 - margin / 100);
          return {
            ...item,
            margin: margin,
            finalPrice,
            isCustomPrice: true,
          };
        }
        return item;
      })
    );
  };

  // Handle custom price change for individual items
  const handleCustomPriceChange = (productId: string, price: number) => {
    if (isNaN(price) || price <= 0) return;

    console.log('💰 [PRICING MANAGEMENT] Updating custom price:', { productId, price });

    const updateItems = activeTab === 'individual' ? setPricingItems : setGlobalPricingItems;

    updateItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          const margin = ((item.basePrice - price) / item.basePrice) * 100;
          return {
            ...item,
            finalPrice: price,
            margin: parseFloat(margin.toFixed(2)),
            isCustomPrice: true,
          };
        }
        return item;
      })
    );
  };

  // Apply global margin to all products
  const applyGlobalPricing = () => {
    const marginValue = parseFloat(globalMargin);
    const markupValue = parseFloat(globalMarkup);

    if ((isNaN(marginValue) || marginValue < 0) && (isNaN(markupValue) || markupValue < 0)) {
      Alert.alert('Error', 'Please enter a valid discount percentage or markup amount');
      return;
    }

    setApplyingGlobalMargin(true);

    setTimeout(() => {
      const updateItems = activeTab === 'individual' ? setPricingItems : setGlobalPricingItems;

      updateItems(prev =>
        prev.map(item => {
          let finalPrice = item.basePrice;
          let margin = 0;

          if (!isNaN(marginValue) && marginValue >= 0) {
            finalPrice = item.basePrice * (1 - marginValue / 100);
            margin = marginValue;
          } else if (!isNaN(markupValue) && markupValue >= 0) {
            finalPrice = item.basePrice - markupValue;
            margin = ((item.basePrice - finalPrice) / item.basePrice) * 100;
          }

          return {
            ...item,
            margin: parseFloat(margin.toFixed(2)),
            finalPrice: Math.max(finalPrice, 0), // Ensure price doesn't go negative
            isCustomPrice: false
          };
        })
      );

      setApplyingGlobalMargin(false);
      setGlobalMargin('');
      setGlobalMarkup('');

      Alert.alert('Success', 'Global pricing applied successfully');
    }, 1000);
  };

  // Save pricing changes
  const savePricing = async () => {
    if (!currentUser?.id) {
      Alert.alert('Error', 'Missing user information');
      return;
    }

    if (!canEditPrices()) {
      Alert.alert('Error', 'You do not have permission to save pricing');
      return;
    }
    // Check if we have users selected or if it's global pricing
    if (!isBulkMode && !selectedUsers?.length && activeTab !== 'global') {
      Alert.alert('Error', 'Please select users or use global pricing');
      return;
    }

    setIsLoading(true);

    try {
      // Prepare catalog IDs with their discount percentages
      const catalogDiscounts = filteredItems.reduce((acc: {[key: string]: string}, item) => {
        acc[item.productId] = `${item.margin}%`;
        return acc;
      }, {});

      
      const isGlobalPricing = activeTab === 'global';
      const targetUsers = isBulkMode && selectedUsers ? selectedUsers : [];

      console.log('🚀 [PRICING MANAGEMENT] Saving pricing:', {
        currentUserRole: currentUser.role,
        selectedUsersCount: targetUsers.length,
        catalogDiscounts,
        isGlobal: isGlobalPricing,
        isBulkMode,
      });

      if (currentUser.role === UserRole.SUPER_STOCKIST) {
        // Create distributor pricing
        const payload: DistributorPriceRequest = {
          id: Number(currentUser.id),
          distributorIds: isGlobalPricing ? [-1] : targetUsers.map(user => Number(user.id)),
          catalogIds: Object.keys(catalogDiscounts).length > 0 ? [catalogDiscounts] : undefined,
          globalPercentage: isGlobalPricing && Object.keys(catalogDiscounts).length === 0 ? 
            `${parseFloat(globalMargin) || 10}%` : undefined,
        };

        const response = await createDistributorPrice({
          data: payload,
          isGlobal: isGlobalPricing
        }).unwrap();

        console.log('✅ Distributor pricing created:', response);

      } else if (currentUser.role === UserRole.DISTRIBUTOR) {
        // Create retailer pricing
        const payload: RetailerPriceRequest = {
          id: Number(currentUser.id),
          retailerIds: isGlobalPricing ? [-1] : targetUsers.map(user => Number(user.id)),
          catalogIds: Object.keys(catalogDiscounts).length > 0 ? [catalogDiscounts] : undefined,
          globalPercentage: isGlobalPricing && Object.keys(catalogDiscounts).length === 0 ? 
            `${parseFloat(globalMargin) || 10}%` : undefined,
        };

        const response = await createRetailerPrice({
          data: payload,
          isGlobal: isGlobalPricing
        }).unwrap();

        console.log('✅ Retailer pricing created:', response);
      } else {
        throw new Error('Invalid user role for pricing management');
      }
      const userCountText = isGlobalPricing ? 'all users' :
        `${targetUsers.length} user${targetUsers.length > 1 ? 's' : ''}`;
      Alert.alert('Success', `Pricing updated successfully for ${userCountText}.`);
      navigation.goBack();

    } catch (error: any) {
      console.error('❌ Failed to save pricing:', error);
      
      let errorMessage = 'Failed to update pricing. Please try again.';
      if (error?.data) {
        if (typeof error.data === 'string') {
          errorMessage = error.data;
        } else if (error.data.message) {
          errorMessage = error.data.message;
        }
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user can edit prices
  const canEditPrices = (): boolean => {
    if (!currentUser) return false;
    return [UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role);
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    <IconButton
      icon="content-save"
      iconColor="white"
      size={24}
      onPress={savePricing}
      disabled={isLoading || isCreatingDistributorPrice || isCreatingRetailerPrice}
    />
  );

  // Category filter options
  const categoryOptions = [
    { id: 'all', label: 'All Categories' },
    ...categories.map(category => ({ id: category, label: category }))
  ];

  // Handle category filter change
  const handleCategoryFilterChange = (categoryId: string) => {
    setSelectedCategory(categoryId === 'all' ? null : categoryId);
  };

  // Render content
  const renderContent = () => {
    if (filteredItems.length === 0) {
      return (
        <EmptyState
          icon="attach-money"
          message={searchQuery || selectedCategory ? 'No products match your filters' : 'No products found'}
        />
      );
    }

    return (
      <FlatList
        data={filteredItems}
        renderItem={({ item }) => (
          <EnhancedPricingCard
            item={item}
            onMarginChange={handleMarginChange}
            onCustomPriceChange={handleCustomPriceChange}
            canEdit={canEditPrices()}
            isGlobalMode={activeTab === 'global'}
            globalMargin={parseFloat(globalMargin) || 0}
          />
        )}
        keyExtractor={item => item.productId}
        contentContainerStyle={styles.pricingList}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  // Render bulk users info
  const renderBulkUsersInfo = () => {
    if (!isBulkMode || !selectedUsers) return null;

    return (
      <Surface style={styles.bulkUsersContainer} elevation={1}>
        <Text variant="titleMedium" style={styles.bulkUsersTitle}>
          Managing Pricing for {selectedUsers.length} Users
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.userChipsContainer}>
          {selectedUsers.map((user: User) => (
            <Chip
              key={user.id}
              mode="flat"
              style={styles.userChip}
              textStyle={styles.userChipText}
            >
              {user.name}
            </Chip>
          ))}
        </ScrollView>
      </Surface>
    );
  };

  return (
    <BaseManagementScreen
      title={isBulkMode ? "Bulk Pricing Management" : "Pricing Management"}
      showBack={true}
      rightActions={renderHeaderRightActions()}
      subtitle={displayName}
      isLoading={isLoading}
      loadingText="Loading pricing data..."
    >
      {/* Bulk Users Info */}
      {renderBulkUsersInfo()}

      <Surface style={styles.tabContainer} elevation={1}>
        <View style={styles.tabs}>
          <Button
            mode={activeTab === 'individual' ? 'contained' : 'text'}
            onPress={() => switchTab('individual')}
            style={[styles.tab, activeTab === 'individual' && styles.activeTab]}
            labelStyle={activeTab === 'individual' ? styles.activeTabLabel : styles.tabLabel}
          >
            {isBulkMode ? 'Individual Pricing' : 'Individual Pricing'}
          </Button>
          <Button
            mode={activeTab === 'global' ? 'contained' : 'text'}
            onPress={() => switchTab('global')}
            style={[styles.tab, activeTab === 'global' && styles.activeTab]}
            labelStyle={activeTab === 'global' ? styles.activeTabLabel : styles.tabLabel}
          >
            Global Pricing
          </Button>
        </View>
      </Surface>

      {/* Global Margin - Only show in Global tab */}
      {activeTab === 'global' && (
        <Surface style={styles.globalMarginContainer} elevation={1}>
          <Text variant="titleMedium" style={styles.globalMarginTitle}>
            {isBulkMode ? `Set Global Pricing for ${selectedUsers?.length} Users` : 'Set Global Pricing'}
          </Text>
          <View style={styles.globalMarginInputContainer}>
            <View style={styles.inputGroup}>
              <TextInput
                mode="outlined"
                label="Discount Percentage"
                placeholder="Enter discount percentage"
                value={globalMargin}
                onChangeText={setGlobalMargin}
                keyboardType="numeric"
                style={[styles.globalMarginInput, { marginBottom: 8 }]}
                right={<TextInput.Affix text="%" />}
                disabled={applyingGlobalMargin}
              />
              <TextInput
                mode="outlined"
                label="Discount Amount"
                placeholder="Enter discount amount"
                value={globalMarkup}
                onChangeText={setGlobalMarkup}
                keyboardType="numeric"
                style={styles.globalMarginInput}
                right={<TextInput.Affix text="₹" />}
                disabled={applyingGlobalMargin}
              />
            </View>
            <Button
              mode="contained"
              onPress={applyGlobalPricing}
              disabled={(!globalMargin && !globalMarkup) || applyingGlobalMargin}
              style={styles.applyButton}
              loading={applyingGlobalMargin}
            >
              {isBulkMode ? 'Apply to All Users' : 'Apply'}
            </Button>
          </View>
        </Surface>
      )}

      {/* Search and Filters */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search products..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FilterChips
          options={categoryOptions}
          selectedId={selectedCategory || 'all'}
          onSelect={handleCategoryFilterChange}
        />
      </View>

      {/* Content */}
      {renderContent()}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: 'white',
    marginBottom: 8,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    borderRadius: 0,
  },
  activeTab: {
    backgroundColor: 'transparent',
  },
  tabLabel: {
    color: '#6b7280',
    fontSize: 14,
  },
  activeTabLabel: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: 'bold',
  },
  globalMarginContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  globalMarginTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
  },
  inputGroup: {
    width: 'auto',
    height: 120,
  },
  globalMarginInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  globalMarginInput: {
    flex: 1,
    marginRight: 12,
  },
  applyButton: {
    height: 50,
    justifyContent: 'center',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 8,
  },
  pricingList: {
    padding: 16,
    paddingBottom: 80,
  },
  bulkUsersContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  bulkUsersTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  userChipsContainer: {
    flexDirection: 'row',
  },
  userChip: {
    marginRight: 8,
    backgroundColor: '#ddd6fe',
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
  },
});

export default PricingManagementScreen;
