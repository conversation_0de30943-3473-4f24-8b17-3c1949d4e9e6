import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, TextInput, Image, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useGetAllUsersQuery } from './api/apiSlice';
import { useUser } from '../../context/UserContext';

type RootStackParamList = {
  SuperStockistDetail: { id?: string | number };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'SuperStockistDetail'>;

// Define API response type
interface SuperStockistApiData {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  status: number;
  isUserVerified: string;
}

const SuperStockistListScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const {currentUser} = useUser(); 
  const userId = currentUser?.id; // Get user ID from context, default to 0 if not available
  
  // Use the query hook to fetch data
  const { data: superStockistData = [], isLoading, error } = useGetAllUsersQuery(userId);
    
  // Filter super stockists based on search query
  const filteredSuperStockists = superStockistData.filter(
    (ss: SuperStockistApiData) => 
      ss.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ss.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ss.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ss.mobileNumber.includes(searchQuery)
  );

  const renderItem = ({ item }: { item: SuperStockistApiData }) => (
    <TouchableOpacity 
      style={styles.itemContainer}
      onPress={() => navigation.navigate('SuperStockistDetail', { id: item.id })}
    >
      <View style={styles.itemHeader}>
        <View style={styles.avatarContainer}>
          <View style={[styles.avatar, styles.placeholderAvatar]}>
            <Text style={styles.placeholderText}>{item.firstName.charAt(0)}</Text>
          </View>
        </View>
        <View style={styles.infoContainer}>
          <Text style={styles.nameText}>{`${item.firstName} ${item.lastName}`}</Text>
          <Text style={styles.emailText}>{item.email}</Text>
        </View>
        <View style={[styles.statusBadge, item.status === 1 ? styles.activeBadge : styles.inactiveBadge]}>
          <Text style={styles.statusText}>{item.isUserVerified}</Text>
        </View>
      </View>
      
      <View style={styles.itemDetails}>
        <View style={styles.detailItem}>
          <Icon name="phone" size={16} color="#6b7280" />
          <Text style={styles.detailText}>{item.mobileNumber}</Text>
        </View>
      </View>
      
      <View style={styles.itemFooter}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('SuperStockistDetail', { id: item.id })}
        >
          <Text style={styles.actionButtonText}>View Details</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#9ca3af" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search super stockists..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Icon name="close" size={20} color="#9ca3af" />
          </TouchableOpacity>
        )}
      </View>
      
      <TouchableOpacity 
        style={styles.addButton}
        onPress={() => navigation.navigate('SuperStockistDetail')}
      >
        <Icon name="add" size={24} color="white" />
        <Text style={styles.addButtonText}>Add Super Stockist</Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Icon name="people" size={64} color="#e5e7eb" />
      <Text style={styles.emptyText}>No super stockists found</Text>
      <TouchableOpacity 
        style={styles.emptyButton}
        onPress={() => navigation.navigate('SuperStockistDetail')}
      >
        <Text style={styles.emptyButtonText}>Add Super Stockist</Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading super stockists...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Icon name="error-outline" size={64} color="#ef4444" />
        <Text style={styles.errorText}>Failed to load super stockists</Text>
        <TouchableOpacity style={styles.retryButton}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredSuperStockists}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    color: '#ef4444',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontWeight: 'bold',
  },
  // Rest of the styles remain unchanged
  headerContainer: {
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: '#1f2937',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 12,
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  listContent: {
    paddingBottom: 20,
  },
  itemContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  placeholderAvatar: {
    backgroundColor: '#6366f1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  infoContainer: {
    flex: 1,
  },
  nameText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  emailText: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activeBadge: {
    backgroundColor: '#d1fae5',
  },
  inactiveBadge: {
    backgroundColor: '#fee2e2',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    color: '#10b981',
  },
  itemDetails: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#4b5563',
  },
  itemFooter: {
    padding: 16,
  },
  actionButton: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#4b5563',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    marginTop: 12,
    marginBottom: 20,
    fontSize: 16,
    color: '#9ca3af',
  },
  emptyButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SuperStockistListScreen;
