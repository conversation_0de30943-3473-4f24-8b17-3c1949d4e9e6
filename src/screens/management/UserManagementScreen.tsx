import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  FAB,
  useTheme,
  IconButton,
  Menu,
  Button,
} from 'react-native-paper';
import { useUser } from '../../context/UserContext';
import { User, UserRole } from '../../data/mockData';
import UserCard from '../../components/management/UserCard';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  useGetAllUsersQuery,
  useGetAllPendingUsersQuery,
  useApproveUserMutation
} from './api/apiSlice';
import Icon from 'react-native-vector-icons/MaterialIcons';

// API Response interface
interface ApiUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  status: number;
  isUserVerified: string;
  createdAt?: string;
}

type RootStackParamList = {
  UserManagement: undefined;
  MainApp: undefined;
  CreateUser: { parentRole: UserRole; childRole: UserRole };
  PricingList: { userId?: string; userName?: string; userRole?: UserRole };
  SchemeManagement: { userId?: string; userName?: string; userRole?: UserRole };
  OffersManagement: { userId?: string; userName?: string; userRole?: UserRole };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'UserManagement'>;

const UserManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { currentUser, hasPermission } = useUser();
  const theme = useTheme();

  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'name' | 'date'>('name');
  const [viewMode, setViewMode] = useState<'all' | 'pending'>('all');
  const [managementMenuVisible, setManagementMenuVisible] = useState(false);

  // Use both API query hooks
  const {
    data: allApiUsers = [],
    isLoading: isLoadingAll,
    error: errorAll,
    refetch: refetchAll,
    isFetching: isFetchingAll
  } = useGetAllUsersQuery(currentUser?.id);

  const {
    data: pendingApiUsers = [],
    isLoading: isLoadingPending,
    error: errorPending,
    refetch: refetchPending,
    isFetching: isFetchingPending
  } = useGetAllPendingUsersQuery(currentUser?.id);

  // Use the approve user mutation hook
  const [approveUser, { isLoading: isApprovingUser }] = useApproveUserMutation();

  // Transform API data to User format
  const transformApiUserToUser = (apiUser: ApiUser): User => {
    const getStatusString = (status: number, isUserVerified: string): 'active' | 'inactive' | 'pending' => {
      if (isUserVerified === 'PENDING') {
        return 'pending';
      }

      if (isUserVerified === 'VERIFIED') {
        switch (status) {
          case 1:
            return 'active';
          case 0:
            return 'inactive';
          default:
            return 'active';
        }
      }

      if (isUserVerified === 'REJECTED' || isUserVerified === 'BLOCKED') {
        return 'inactive';
      }

      switch (status) {
        case 1:
          return 'active';
        case 0:
          return 'inactive';
        case 2:
          return 'pending';
        default:
          return 'pending';
      }
    };

    const getChildRole = (): UserRole => {
      if (!currentUser) return UserRole.RETAILER;

      switch (currentUser.role) {
        case UserRole.OOGE_TEAM:
          return UserRole.SUPER_STOCKIST;
        case UserRole.SUPER_STOCKIST:
          return UserRole.DISTRIBUTOR;
        case UserRole.DISTRIBUTOR:
          return UserRole.RETAILER;
        default:
          return UserRole.RETAILER;
      }
    };

    const transformedUser = {
      id: apiUser.id.toString(),
      name: `${apiUser.firstName} ${apiUser.lastName}`.trim(),
      email: apiUser.email,
      phone: apiUser.mobileNumber,
      role: getChildRole(),
      status: getStatusString(apiUser.status, apiUser.isUserVerified),
      createdAt: apiUser.createdAt ? apiUser.createdAt.split(' ')[0] : new Date().toISOString().split('T')[0],
      apiData: apiUser
    };

    return transformedUser;
  };

  // Get current data based on view mode
  const getCurrentApiUsers = () => {
    if (viewMode === 'pending') {
      return pendingApiUsers;
    } else {
      const allUserIds = new Set(allApiUsers.map(user => user.id));
      const uniquePendingUsers = pendingApiUsers.filter(user => !allUserIds.has(user.id));
      return [...allApiUsers, ...uniquePendingUsers];
    }
  };

  const getCurrentLoading = () => {
    return viewMode === 'pending' ? isLoadingPending : isLoadingAll;
  };

  const getCurrentError = () => {
    return viewMode === 'pending' ? errorPending : errorAll;
  };

  const getCurrentFetching = () => {
    return viewMode === 'pending' ? isFetchingPending : (isFetchingAll || isFetchingPending);
  };

  const getCurrentRefetch = () => {
    return () => {
      if (viewMode === 'pending') {
        refetchPending();
      } else {
        refetchAll();
        refetchPending();
      }
    };
  };

  // Transform API users to User format
  const childUsers: User[] = getCurrentApiUsers().map(transformApiUserToUser);

  // Determine which child role the current user can create
  const getChildRole = (): UserRole | null => {
    if (!currentUser) return null;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return null;
    }
  };

  // Get the role name for display
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return 'Ooge Team';
      case UserRole.SUPER_STOCKIST:
        return 'Super Stockist';
      case UserRole.DISTRIBUTOR:
        return 'Distributor';
      case UserRole.RETAILER:
        return 'Retailer';
      default:
        return 'User';
    }
  };

  // Handle API errors
  useEffect(() => {
    const currentError = getCurrentError();
    if (currentError) {
      console.error('Error loading users from API:', currentError);
      Alert.alert('Error', 'Failed to load users. Please try again.');
    }
  }, [getCurrentError()]);

  // Enhanced permission check for creating child users
  const canCreateChildUser = (): boolean => {
    if (!currentUser) {
      return false;
    }

    if (currentUser.role === UserRole.RETAILER || currentUser.role === UserRole.PUBLIC) {
      return false;
    }

    let canCreate = false;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        canCreate = hasPermission('create') || true;
        break;
      case UserRole.SUPER_STOCKIST:
        canCreate = hasPermission('assign') || hasPermission('create') || true;
        break;
      case UserRole.DISTRIBUTOR:
        canCreate = hasPermission('assign') || hasPermission('create') || true;
        break;
      default:
        canCreate = false;
    }

    return canCreate;
  };

  // Navigate to create user screen
  const handleCreateUser = () => {
    const childRole = getChildRole();

    if (!childRole) {
      Alert.alert('Error', 'You do not have permission to create users');
      return;
    }

    navigation.navigate('CreateUser', {
      parentRole: currentUser!.role,
      childRole
    });
  };

  // Handle status filter change
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status === 'all' ? null : status);
  };

  // Apply filters to users
  const getFilteredUsers = () => {
    let filtered = [...childUsers];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        user =>
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    // Apply sorting
    if (sortOrder === 'name') {
      filtered.sort((a, b) => a.name.localeCompare(b.name));
    } else {
      filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }

    return filtered;
  };

  const finalFilteredUsers = getFilteredUsers();

  // Handle navigation to management screens
  const handlePricingManagement = () => {
    setManagementMenuVisible(false);
    navigation.navigate('PricingList', { 
      userRole: currentUser?.role // Assuming currentUser is available in your context
    });
  };

  const handleSchemeManagement = () => {
    setManagementMenuVisible(false);
    navigation.navigate('SchemeManagement', {});
  };

  const handleOffersManagement = () => {
    setManagementMenuVisible(false);
    navigation.navigate('OffersManagement', {});
  };

  const handleStatusPress = (user: User) => {
    const originalApiUser = user.apiData as ApiUser;
    const childId = originalApiUser.id;
    const parentId = currentUser?.id;

    if (!parentId) {
      Alert.alert('Error', 'Parent ID not found. Please log in again.');
      return;
    }

    const updateUserStatus = async (userVerified: string) => {
      try {
        const result = await approveUser({
          childId,
          parentId: parseInt(parentId),
          userVerified
        }).unwrap();

        refetchAll();
        refetchPending();

        const statusMessages = {
          'verified': 'User has been approved and activated!',
          'rejected': 'User has been rejected and blocked!',
          'pending': 'User status has been set to pending!'
        };

        Alert.alert(
          'Success',
          statusMessages[userVerified as keyof typeof statusMessages] || 'User status updated successfully!',
          [{ text: 'OK' }]
        );
      } catch (error: any) {
        console.error('Error updating user status:', error);

        let errorMessage = 'Failed to update user status. Please try again.';

        if (error?.status === 403) {
          errorMessage = 'Permission denied. You may not have permission to update this user\'s status.';
        } else if (error?.status === 404) {
          errorMessage = 'User not found or API endpoint not available.';
        } else if (error?.status === 401) {
          errorMessage = 'Authentication failed. Please log in again.';
        } else if(error?.status === 500){
          errorMessage = 'Something went wrong.';
        }else if (error?.data?.message) {
          errorMessage = `Error: ${error.data.message}`;
        } else if (error?.message) {
          errorMessage = `Error: ${error.message}`;
        } else if (error?.error) {
          errorMessage = `Error: ${error.error}`;
        }

        Alert.alert('Error', errorMessage, [{ text: 'OK' }]);
      }
    };

    const createAlertOptions = () => {
      const alertOptions = [];
      const currentStatus = user.status;
      const isUserVerified = originalApiUser.isUserVerified;

      if (isUserVerified === 'PENDING') {
        alertOptions.push({
          text: '✅ Approve User',
          onPress: () => updateUserStatus('verified'),
          style: 'default' as const
        });
        alertOptions.push({
          text: '❌ Reject User',
          onPress: () => updateUserStatus('rejected'),
          style: 'destructive' as const
        });
      }
      else if (isUserVerified === 'VERIFIED') {
        if (currentStatus === 'active') {
          alertOptions.push({
            text: '🔒 Deactivate User',
            onPress: () => updateUserStatus('rejected'),
            style: 'destructive' as const
          });
        } else {
          alertOptions.push({
            text: '🔓 Activate User',
            onPress: () => updateUserStatus('verified'),
            style: 'default' as const
          });
        }
        alertOptions.push({
          text: '⏳ Set to Pending',
          onPress: () => updateUserStatus('pending'),
          style: 'default' as const
        });
      }
      else if (isUserVerified === 'REJECTED' || isUserVerified === 'BLOCKED') {
        alertOptions.push({
          text: '✅ Approve User',
          onPress: () => updateUserStatus('verified'),
          style: 'default' as const
        });
        alertOptions.push({
          text: '⏳ Set to Pending',
          onPress: () => updateUserStatus('pending'),
          style: 'default' as const
        });
      }
      else {
        alertOptions.push({
          text: '✅ Approve User',
          onPress: () => updateUserStatus('verified'),
          style: 'default' as const
        });
        alertOptions.push({
          text: '❌ Reject User',
          onPress: () => updateUserStatus('rejected'),
          style: 'destructive' as const
        });
        alertOptions.push({
          text: '⏳ Set to Pending',
          onPress: () => updateUserStatus('pending'),
          style: 'default' as const
        });
      }

      alertOptions.push({ text: 'Cancel', style: 'cancel' as const });
      return alertOptions;
    };

    const isUserVerified = originalApiUser.isUserVerified;
    const alertTitle = isUserVerified === 'PENDING' ? 'Approve Pending User' : 'Change User Status';
    const alertMessage = isUserVerified === 'PENDING'
      ? `${user.name} is waiting for approval. What would you like to do?`
      : `Current status: ${user.status.toUpperCase()} (${isUserVerified})\n\nSelect an action for ${user.name}:`;

    Alert.alert(alertTitle, alertMessage, createAlertOptions());
  };

  const handleUserPress = (user: User) => {
    Alert.alert('User Details', `View details for ${user.name}`);
  };

  // Handle view mode change
  const handleViewModeChange = (value: string) => {
    setViewMode(value as 'all' | 'pending');
    setStatusFilter(null);
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    <View style={{ flexDirection: 'row' }}>
      {/* Management Dropdown Menu */}
    <Menu
      visible={managementMenuVisible}
      onDismiss={() => setManagementMenuVisible(false)}
      anchor={
        <IconButton
          icon="dots-vertical"  // Changed from "more-vert"
          iconColor="white"
          size={24}
          onPress={() => setManagementMenuVisible(true)}
        />
      }
      contentStyle={styles.menuContent}
    >
      <Menu.Item
        onPress={handlePricingManagement}
        title="Pricing Management"
        leadingIcon="currency-usd"  // Changed from "attach-money"
        titleStyle={styles.menuItemTitle}
      />
      <Menu.Item
        onPress={handleSchemeManagement}
        title="Scheme Management"
        leadingIcon="tag-outline"  // Changed from "local-offer"
        titleStyle={styles.menuItemTitle}
      />
      <Menu.Item
        onPress={handleOffersManagement}
        title="Offers Management"
        leadingIcon="gift-outline"  // Changed from "card-giftcard"
        titleStyle={styles.menuItemTitle}
      />
    </Menu>
      <IconButton
        icon={sortOrder === 'name' ? 'sort-alphabetical-ascending' : 'sort-calendar-descending'}
        iconColor="white"
        size={24}
        onPress={() => setSortOrder(sortOrder === 'name' ? 'date' : 'name')}
      />
    </View>
  );

  // Status filter options
  const getStatusOptions = () => {
    const baseOptions = [
      { id: 'all', label: 'All' },
      { id: 'active', label: 'Active' },
      { id: 'inactive', label: 'Inactive' }
    ];

    if (viewMode === 'all') {
      baseOptions.push({ id: 'pending', label: `Pending` });
    }

    return baseOptions;
  };

  // Render content
  const renderContent = () => {
    if (finalFilteredUsers.length === 0) {
      let emptyMessage = '';
      let emptyIcon = 'people';
      
      if (viewMode === 'pending') {
        if (searchQuery || statusFilter) {
          emptyMessage = 'No pending users match your filters';
        } else {
          emptyMessage = 'No users are pending approval';
          emptyIcon = 'check-circle';
        }
      } else {
        if (searchQuery || statusFilter) {
          emptyMessage = 'No users match your filters';
        } else {
          emptyMessage = 'No users found';
        }
      }
      
      return (
        <EmptyState
          icon={emptyIcon}
          message={emptyMessage}
          actionLabel={canCreateChildUser() && viewMode === 'all' ? `Create ${getChildRole() ? getRoleName(getChildRole()!) : 'User'}` : undefined}
          onAction={canCreateChildUser() && viewMode === 'all' ? handleCreateUser : undefined}
        />
      );
    }

    return (
      <FlatList
        data={finalFilteredUsers}
        renderItem={({ item }) => (
          <UserCard
            user={item}
            onStatusPress={handleStatusPress}
            onUserPress={handleUserPress}
          />
        )}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.userList}
        showsVerticalScrollIndicator={false}
        refreshing={getCurrentFetching()}
        onRefresh={getCurrentRefetch()}
      />
    );
  };

  return (
    <BaseManagementScreen
      title={viewMode === 'pending'
        ? `Pending ${getChildRole() ? getRoleName(getChildRole()!) + 's' : 'Users'}`
        : `Manage ${getChildRole() ? getRoleName(getChildRole()!) + 's' : 'Users'}`}
      showBack={false}
      rightActions={renderHeaderRightActions()}
      isLoading={getCurrentLoading() || isApprovingUser}
      loadingText={isApprovingUser ? "Updating user status..." : "Loading users..."}
    >
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder={`Search ${viewMode === 'pending' ? 'pending ' : ''}${getChildRole() ? getRoleName(getChildRole()!) + 's' : 'users'}...`}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FilterChips
          options={getStatusOptions()}
          selectedId={statusFilter || 'all'}
          onSelect={handleStatusFilterChange}
        />
      </View>

      {renderContent()}

      {/* Create User FAB */}
      {canCreateChildUser() && getChildRole() && viewMode === 'all' && (
        <FAB
          icon="plus"
          style={[styles.createUserFab, { backgroundColor: theme.colors.primary }]}
          onPress={handleCreateUser}
          color="white"
          label={`Add ${getRoleName(getChildRole()!)}`}
        />
      )}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 0,
  },
  userList: {
    padding: 16,
    paddingBottom: 120,
  },
  createUserFab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  menuContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    elevation: 8,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default UserManagementScreen;
