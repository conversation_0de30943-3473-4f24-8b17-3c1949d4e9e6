import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

// Types for Pricing
export interface DistributorPriceRequest {
  id: number; // stockist ID
  distributorIds?: number[]; // [-1] for all distributors, or specific distributor IDs
  catalogIds?: Array<{[catalogId: string]: string}>; // catalog ID to discount percentage mapping
  globalPercentage?: string; // for global pricing like "10%"
}

export interface RetailerPriceRequest {
  id: number; // distributor ID
  retailerIds?: number[]; // [-1] for all retailers, or specific retailer IDs
  catalogIds?: Array<{[catalogId: string]: string}>; // catalog ID to discount percentage mapping
  globalPercentage?: string; // for global pricing like "10%"
}

export interface PriceResponse {
  data: {
    catalogDetails: Array<{
      catalogId: number;
      catalogName: string;
      distributors?: Array<{
        distributorId: number;
        distributorName: string;
        variantPrices: Array<{
          productId: number;
          variantId: number;
          price: number;
          discountPrice: number;
          discountPercentage: number;
        }>;
      }>;
      retailers?: Array<{
        retailerId: number;
        retailerName: string;
        variantPrices: Array<{
          productId: number;
          variantId: number;
          price: number;
          discountPrice: number;
          discountPercentage: number;
        }>;
      }>;
    }>;
  };
}

// Types for Pricing List/Management
export interface PricingRule {
  id: number;
  name: string;
  description: string;
  type: 'DISTRIBUTOR' | 'RETAILER';
  userIds: number[];
  catalogIds: string[];
  discountPercentage: number;
  isGlobal: boolean;
  status: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface GetPricingRulesRequest {
  userId?: number;
  type?: string;
  status?: number;
  page?: number;
  size?: number;
}

export interface GetPricingRulesResponse {
  data: PricingRule[];
  pageNumber: number;
  pageSize: number;
  totalElements: number;
  totalPages: number;
  last: boolean;
}

// Types for Offer
export interface Offer {
  id?: number;
  title: string;
  imageUrl: string;
  userId: number; // -1 for all users, specific userId for individual user
  region: string; // "ALL" for all regions, specific region name
  state: string; // "ALL" for all states, specific state name
  city: string; // "ALL" for all cities, specific city name
  area: string; // "ALL" for all areas, specific area name
  startDate: string; // Format: "2025-06-01 00:00:00"
  endDate: string; // Format: "2025-06-15 23:59:59"
  status: number; // 1 for active, 0 for inactive
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateOfferRequest {
  title: string;
  imageUrl: string;
  userId: number;
  region: string;
  state: string;
  city: string;
  area: string;
  startDate: string;
  endDate: string;
  status: number;
  createdBy: number;
  updatedBy: number;
}

export interface UpdateOfferRequest {
  title: string;
  imageUrl: string;
  userId: number;
  region: string;
  state: string;
  city: string;
  area: string;
  startDate: string;
  endDate: string;
  status: number;
  updatedBy: number;
}

export interface GetOffersRequest {
  title?: string; // search
  status?: number; // filter
  page?: number;
  size?: number;
}

export interface GetOffersResponse {
  data: Offer[];
  pageNumber: number;
  pageSize: number;
  totalElements: number;
  totalPages: number;
  last: boolean;
}

export interface OfferResponse {
  data: Offer;
}

// Create the API slice
export const pricingApi = createApi({
  reducerPath: 'pricingApi',
  baseQuery: async ({ url, method, body, params }) => {
    try {
      // Log the request details for debugging
      console.log('📤 [PRICING API] Request:', { url, method, body, params });

      let result;
      switch (method?.toUpperCase() || 'GET') {
        case 'GET':
          result = await AuthApiService.get(url, params);
          break;
        case 'POST':
          result = await AuthApiService.post(url, body);
          break;
        case 'PUT':
          result = await AuthApiService.put(url, body);
          break;
        case 'DELETE':
          result = await AuthApiService.delete(url);
          break;
        default:
          result = await AuthApiService.get(url, params);
      }

      console.log('✅ [PRICING API] Success Response:', result);
      return { data: result };
    } catch (error: any) {
      console.error('❌ [PRICING API] Error:', error.response || error);
      return {
        error: {
          status: error.response?.status || 500,
          data: error.response?.data || { message: error.message }
        }
      };
    }
  },
  tagTypes: ['Offer', 'DistributorPrice', 'RetailerPrice'],
  endpoints: (builder) => ({
    // Create Distributor Price
    createDistributorPrice: builder.mutation<PriceResponse, { data: DistributorPriceRequest; isGlobal?: boolean }>({
      query: ({ data, isGlobal = false }) => {
        console.log('🚀 [PRICING API] Creating Distributor Price:', {
          endpoint: `api/v1/distributor/create-distributor-price${isGlobal ? '?isGlobal=true' : ''}`,
          method: 'POST',
          payload: data,
          isGlobal
        });
        return {
          url: `api/v1/distributor/create-distributor-price${isGlobal ? '?isGlobal=true' : ''}`,
          method: 'POST',
          body: data,
        };
      },
      invalidatesTags: ['DistributorPrice'],
    }),

    // Create Retailer Price
    createRetailerPrice: builder.mutation<PriceResponse, { data: RetailerPriceRequest; isGlobal?: boolean }>({
      query: ({ data, isGlobal = false }) => {
        console.log('🚀 [PRICING API] Creating Retailer Price:', {
          endpoint: `api/v1/retailer/create-retailer-price${isGlobal ? '?isGlobal=true' : ''}`,
          method: 'POST',
          payload: data,
          isGlobal
        });
        return {
          url: `api/v1/retailer/create-retailer-price${isGlobal ? '?isGlobal=true' : ''}`,
          method: 'POST',
          body: data,
        };
      },
      invalidatesTags: ['RetailerPrice'],
    }),
    // Create Offer
    createOffer: builder.mutation<OfferResponse, CreateOfferRequest>({
      query: (offerData) => ({
        url: 'api/v1/catalog/offer',
        method: 'POST',
        body: offerData,
      }),
      invalidatesTags: ['Offer'],
    }),

    // Update Offer
    updateOffer: builder.mutation<OfferResponse, { id: number; data: UpdateOfferRequest }>({
      query: ({ id, data }) => ({
        url: `api/v1/catalog/offer/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Offer'],
    }),

    // Get Offer By ID
    getOfferById: builder.query<OfferResponse, number>({
      query: (id) => `api/v1/catalog/offer/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Offer', id }],
    }),

    // Get All Offers
    getAllOffers: builder.mutation<GetOffersResponse, GetOffersRequest>({
      query: (params) => ({
        url: 'api/v1/catalog/offers',
        method: 'POST',
        body: params,
      }),
      invalidatesTags: ['Offer'],
    }),

    // Delete Offer (if needed)
    deleteOffer: builder.mutation<void, number>({
      query: (id) => ({
        url: `api/v1/catalog/offer/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Offer'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Pricing hooks
  useCreateDistributorPriceMutation,
  useCreateRetailerPriceMutation,
  // Offer hooks
  useCreateOfferMutation,
  useUpdateOfferMutation,
  useGetOfferByIdQuery,
  useGetAllOffersMutation,
  useDeleteOfferMutation,
} = pricingApi;
