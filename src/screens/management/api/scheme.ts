// import { createApi } from '@reduxjs/toolkit/query/react';
// import AuthApiService from '../../../services/api/AuthApiService';

// // Types for Scheme (matching actual API response)
// export interface Scheme {
//   id?: number;
//   name: string;
//   description: string;
//   startDate: string;
//   endDate: string;
//   offer: 'TRIP' | 'GIFT';
//   status: number;
//   createdB?: number; // Note: API returns 'createdB' not 'createdBy'
//   updatedBy?: number;
//   userId: number;
//   purchaseAmount: number;
//   schemeProducts?: SchemeProduct[];
// }

// export interface SchemeProduct {
//   id: number;
//   name: string;
//   schemeUsers?: SchemeUser[];
// }

// export interface SchemeUser {
//   id: number;
//   name: string;
// }

// export interface CreateSchemeRequest {
//   name: string;
//   description: string;
//   startDate: string; // Format: "2025-05-31"
//   endDate: string; // Format: "2025-06-03"
//   offer: 'TRIP' | 'GIFT';
//   userIds: number[];
//   catalogId: number[];
//   purchaseAmount: number;
//   media: 'IMAGE';
//   catalogType: 'SCHEMES';
//   urls:string[];
// }

// export interface UpdateSchemeRequest {
//   userId: number;
//   description: string;
//   startDate: string;
//   endDate: string;
//   offer: 'TRIP' | 'GIFT';
//   purchaseAmount: number;    
//   catalogId: number[];
//   userIds: number[];
//   status: number;
//   media: 'IMAGE';
//   catalogType: 'SCHEMES';
//   urls:string[];
// }

// export interface ApplySchemeRequest {
//   Id: number; //(scheme id)
//   catalogId: number[]; // catalog/product ids
//   userIds: number[]; // user ids to apply scheme to
// }

// export interface ApplySchemeResponse {
//   message?: string;
//   data?: Scheme;
// }

// // Response interfaces matching actual API
// export interface SchemeResponse {
//   message: string;
//   data: Scheme;
// }

// export interface SchemesListResponse {
//   data: Scheme[];
//   page: number;
//   count: number;
//   totalCount: number;
// }

// export interface GetAllSchemesResponse {
//   data: {
//     data: Scheme[];
//     message: string;
//     page: number;
//     count: number;
//     totalCount: number;
//   };
// }

// export interface UpdateSchemeResponse {
//   data: Scheme;
// }

// export interface ApplySchemeResponse {
//   message?: string;
// }

// // Create the API slice
// export const schemeApi = createApi({
//   reducerPath: 'schemeApi',
//   // Custom base query function that uses AuthApiService
//   baseQuery: async ({ url, method, body }) => {
//     try {
//       console.log('Scheme API Request:', { url, method, body });

//       let result;
//       switch (method?.toUpperCase() || 'GET') {
//         case 'GET':
//           result = await AuthApiService.get(url);
//           break;
//         case 'POST':
//           result = await AuthApiService.post(url, body);
//           break;
//         case 'PUT':
//           result = await AuthApiService.put(url, body);
//           break;
//         case 'DELETE':
//           result = await AuthApiService.delete(url);
//           break;
//         default:
//           result = await AuthApiService.get(url);
//       }
//       return { data: result };
//     } catch (error: any) {
//       console.log('Scheme API error:', error.response || error);
//       return {
//         error: {
//           status: error.response?.status,
//           data: error.response?.data || error.message
//         }
//       };
//     }
//   },
//   tagTypes: ['Scheme'],
//   endpoints: (builder) => ({
//     // Create Scheme
//     createScheme: builder.mutation<SchemeResponse, CreateSchemeRequest>({
//       query: (schemeData) => {
//         console.log('🚀 [SCHEME API] Creating Scheme - Request Data:', {
//           endpoint: 'api/v1/scheme/create',
//           method: 'POST',
//           payload: schemeData
//         });
//         return {
//           url: 'api/v1/scheme/create',
//           method: 'POST',
//           body: schemeData,
//         };
//       },
//       transformResponse: (response: any) => {
//         console.log('✅ [SCHEME API] Create Scheme - Success Response:', response);
//         return response;
//       },
//       transformErrorResponse: (response: any) => {
//         console.log('❌ [SCHEME API] Create Scheme - Error Response:', response);
//         return response?.data || response;
//       },
//       invalidatesTags: ['Scheme'],
//     }),

//     // Update Scheme
//     updateScheme: builder.mutation<UpdateSchemeResponse, { id: number; data: UpdateSchemeRequest }>({
//       query: ({ id, data }) => {
//         console.log('🔄 [SCHEME API] Updating Scheme - Request Data:', {
//           endpoint: `api/v1/scheme/update-scheme/${id}`,
//           method: 'PUT',
//           schemeId: id,
//           payload: data
//         });
//         return {
//           url: `api/v1/scheme/update-scheme/${id}`,
//           method: 'PUT',
//           body: data,
//         };
//       },
//       transformResponse: (response: any) => {
//         console.log('✅ [SCHEME API] Update Scheme - Success Response:', response);
//         return response;
//       },
//       transformErrorResponse: (response: any) => {
//         console.log('❌ [SCHEME API] Update Scheme - Error Response:', response);
//         return response?.data || response;
//       },
//       invalidatesTags: ['Scheme'],
//     }),

//     // Get All Schemes
//     getAllSchemes: builder.query<GetAllSchemesResponse, void>({
//       query: () => {
//         console.log('📊 [SCHEME API] Getting All Schemes - Request:', {
//           endpoint: 'api/v1/scheme/getAll',
//           method: 'GET'
//         });
//         return {
//           url: 'api/v1/scheme/getAll',
//           method: 'GET',
//         };
//       },
//       transformResponse: (response: any) => {
//         console.log('✅ [SCHEME API] Get All Schemes - Success Response:', {
//           totalSchemes: response?.data?.data?.length || 0,
//           totalCount: response?.data?.totalCount || 0,
//           response: response
//         });
//         return response;
//       },
//       transformErrorResponse: (response: any) => {
//         console.log('❌ [SCHEME API] Get All Schemes - Error Response:', response);
//         return response?.data || response;
//       },
//       providesTags: ['Scheme'],
//     }),

//     // Get Schemes By User ID
//     getSchemesByUserId: builder.query<SchemesListResponse, number>({
//       query: (userId) => {
//         console.log('👥 [SCHEME API] Getting Schemes By User ID - Request:', {
//           endpoint: `api/v1/scheme/user/${userId}`,
//           method: 'GET',
//           userId: userId
//         });
//         return {
//           url: `api/v1/scheme/user-scheme`,
//           method: 'GET',
//         };
//       },
//       transformResponse: (response: any) => {
//         console.log('✅ [SCHEME API] Get Schemes By User ID - Success Response:', {
//           userId: response?.data?.[0]?.userId,
//           schemesCount: response?.data?.length || 0,
//           totalCount: response?.totalCount || 0,
//           response: response
//         });
//         return response;
//       },
//       transformErrorResponse: (response: any) => {
//         console.log('❌ [SCHEME API] Get Schemes By User ID - Error Response:', response);
//         return response?.data || response;
//       },
//       providesTags: (_result, _error, userId) => [{ type: 'Scheme', id: `user-${userId}` }],
//     }),

//     // Apply Scheme
//     applyScheme: builder.mutation<ApplySchemeResponse, ApplySchemeRequest>({
//       query: (applyData) => {
//         console.log('🎯 [SCHEME API] Applying Scheme - Request Data:', {
//           endpoint: 'api/v1/scheme/apply-scheme',
//           method: 'POST',
//           schemeId: applyData.Id,
//           catalogIds: applyData.catalogId,
//           userIds: applyData.userIds,
//           payload: applyData
//         });
//         return {
//           url: 'api/v1/scheme/apply-scheme',
//           method: 'POST',
//           body: applyData,
//         };
//       },
//       transformResponse: (response: any) => {
//         console.log('✅ [SCHEME API] Apply Scheme - Success Response:', response);
//         return response;
//       },
//       transformErrorResponse: (response: any) => {
//         console.log('❌ [SCHEME API] Apply Scheme - Error Response:', response);
//         return response?.data || response;
//       },
//       invalidatesTags: ['Scheme'],
//     }),
//     // Get Scheme By ID
//     getSchemeById: builder.query<Scheme, number>({
//       query: (schemeId) => ({
//         url: `/api/v1/scheme/get-scheme-info?schemeId=${schemeId}`,
//         method: 'GET',
//       }),
//       transformResponse: (response: any) => response.data,
//       providesTags: (_result, _error, schemeId) => [{ type: 'Scheme', id: schemeId }],
//     })
//   }),
// });

// // Export hooks for usage in functional components
// export const {
//   useCreateSchemeMutation,
//   useUpdateSchemeMutation,
//   useGetAllSchemesQuery,
//   useGetSchemesByUserIdQuery,
//   useGetSchemeByIdQuery,
//   useApplySchemeMutation,
// } = schemeApi;


import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from '../../../services/api/AuthApiService';

// Enhanced Types for Scheme API
export interface Scheme {
  id?: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT';
  status: number;
  createdB?: number;
  updatedBy?: number;
  userId?: number;
  purchaseAmount: number;
  schemeProducts?: SchemeProduct[];
  urls?: string[];
  media?: string;
  catalogType?: string;
}

export interface SchemeProduct {
  id: number;
  name: string;
  schemeUsers?: SchemeUser[];
}

export interface SchemeUser {
  id: number;
  name: string;
}

// Create Scheme Request - Matches your API exactly
export interface CreateSchemeRequest {
  name: string;
  description: string;
  startDate: string; // Format: "2025-06-13"
  endDate: string; // Format: "2025-06-20"
  offer: 'TRIP' | 'GIFT';
  userIds: number[];
  catalogId: number[];
  purchaseAmount: number;
  media: 'IMAGE';
  catalogType: 'SCHEMES';
  urls?: string[];
}

// Create Scheme Response - Matches your API response
export interface CreateSchemeResponse {
  message: string;
  data: {
    id: number;
    name: string;
    description: string;
    status: number;
    createdB: number;
    updatedBy: number;
    schemeProducts: SchemeProduct[];
  };
}

// Update Scheme Request (for future implementation)
export interface UpdateSchemeRequest {
  id: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  offer: 'TRIP' | 'GIFT';
  userIds: number[];
  catalogId: number[];
  purchaseAmount: number;
  media: 'IMAGE';
  catalogType: 'SCHEMES';
  urls: string[];
  status: number;
}

// List responses
export interface SchemesListResponse {
  data: Scheme[];
  page?: number;
  count?: number;
  totalCount?: number;
}

export interface GetAllSchemesResponse {
  data: {
    data: Scheme[];
    message: string;
    page?: number;
    count?: number;
    totalCount?: number;
  };
}

// Error response
export interface SchemeErrorResponse {
  message: string;
}

// Create the API slice
export const schemeApi = createApi({
  reducerPath: 'schemeApi',
  baseQuery: async ({ url, method, body }) => {
    try {
      console.log('🚀 [SCHEME API] Request:', { 
        url, 
        method, 
        body: JSON.stringify(body, null, 2) 
      });

      let result;
      switch (method?.toUpperCase() || 'GET') {
        case 'GET':
          result = await AuthApiService.get(url);
          break;
        case 'POST':
          result = await AuthApiService.post(url, body);
          break;
        case 'PUT':
          result = await AuthApiService.put(url, body);
          break;
        case 'DELETE':
          result = await AuthApiService.delete(url);
          break;
        default:
          result = await AuthApiService.get(url);
      }
      
      console.log('✅ [SCHEME API] Success Response:', result);
      return { data: result };
    } catch (error: any) {
      console.error('❌ [SCHEME API] Error:', {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      });
      
      return {
        error: {
          status: error.response?.status || 500,
          data: error.response?.data || { message: error.message }
        }
      };
    }
  },
  tagTypes: ['Scheme', 'User'],
  endpoints: (builder) => ({
    // Create Scheme - Main focus
    createScheme: builder.mutation<CreateSchemeResponse, CreateSchemeRequest>({
      query: (schemeData) => {
        console.log('📝 [CREATE SCHEME] Request Payload:', {
          endpoint: 'api/v1/scheme/create',
          method: 'POST',
          payload: schemeData,
          validation: {
            hasName: !!schemeData.name,
            hasDescription: !!schemeData.description,
            hasValidDates: schemeData.startDate && schemeData.endDate,
            hasUsers: schemeData.userIds.length > 0,
            hasProducts: schemeData.catalogId.length > 0,
            hasPurchaseAmount: schemeData.purchaseAmount > 0
          }
        });

        return {
          url: 'api/v1/scheme/create',
          method: 'POST',
          body: schemeData,
        };
      },
      transformResponse: (response: CreateSchemeResponse) => {
        console.log('✅ [CREATE SCHEME] Success Response:', {
          message: response.message,
          schemeId: response.data.id,
          schemeName: response.data.name,
          productsCount: response.data.schemeProducts?.length || 0,
          usersCount: response.data.schemeProducts?.[0]?.schemeUsers?.length || 0
        });
        return response;
      },
      transformErrorResponse: (response: any) => {
        console.error('❌ [CREATE SCHEME] Error Response:', {
          status: response.status,
          message: response.data?.message || 'Unknown error',
          fullError: response
        });
        return response?.data || response;
      },
      invalidatesTags: ['Scheme'],
    }),

    // Get All Schemes
    getAllSchemes: builder.query<GetAllSchemesResponse, void>({
      query: () => {
        console.log('📊 [GET ALL SCHEMES] Fetching schemes...');
        return {
          url: 'api/v1/scheme/getAll',
          method: 'GET',
        };
      },
      transformResponse: (response: any) => {
        console.log('✅ [GET ALL SCHEMES] Success:', {
          totalSchemes: response?.data?.data?.length || 0,
          totalCount: response?.data?.totalCount || 0
        });
        return response;
      },
      providesTags: ['Scheme'],
    }),

    // Get Schemes By User ID
    getSchemesByUserId: builder.query<SchemesListResponse, number>({
      query: (userId) => {
        console.log('👤 [GET USER SCHEMES] Fetching for user:', userId);
        return {
          url: `api/v1/scheme/user-scheme`,
          method: 'GET',
        };
      },
      transformResponse: (response: any) => {
        console.log('✅ [GET USER SCHEMES] Success:', {
          schemesCount: response?.data?.length || 0
        });
        return response;
      },
      providesTags: (_result, _error, userId) => [{ type: 'Scheme', id: `user-${userId}` }],
    }),

    // Get Scheme By ID (for future edit functionality)
    getSchemeById: builder.query<Scheme, number>({
      query: (schemeId) => ({
        url: `/api/v1/scheme/get-scheme-info?schemeId=${schemeId}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => response.data,
      providesTags: (_result, _error, schemeId) => [{ type: 'Scheme', id: schemeId }],
    }),

    // Update Scheme (placeholder for future implementation)
    updateScheme: builder.mutation<CreateSchemeResponse, UpdateSchemeRequest>({
      query: ({ id, ...data }) => ({
        url: `api/v1/scheme/update-scheme/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Scheme'],
    }),
  }),
});

// Export hooks
export const {
  useCreateSchemeMutation,
  useGetAllSchemesQuery,
  useGetSchemesByUserIdQuery,
  useGetSchemeByIdQuery,
  useUpdateSchemeMutation, // For future use
} = schemeApi;