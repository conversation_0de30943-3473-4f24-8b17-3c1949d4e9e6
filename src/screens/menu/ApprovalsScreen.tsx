import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Header from '../../components/common/Header';

const ApprovalsScreen = () => {
  const approvals = [
    {
      id: 1,
      type: "Order",
      title: "Bulk Order #12345",
      amount: "₹25,000",
      requestedBy: "John Doe",
      date: "2024-02-15",
      status: "pending"
    },
    {
      id: 2,
      type: "Discount",
      title: "Special Discount Request",
      amount: "15%",
      requestedBy: "<PERSON>",
      date: "2024-02-14",
      status: "pending"
    },
    {
      id: 3,
      type: "Return",
      title: "Product Return Request",
      amount: "₹5,000",
      requestedBy: "<PERSON> Johnson",
      date: "2024-02-13",
      status: "pending"
    }
  ];

  const getIcon = (type: string) => {
    switch (type) {
      case 'Order': return 'shopping-cart';
      case 'Discount': return 'local-offer';
      case 'Return': return 'assignment-return';
      default: return 'error';
    }
  };

  return (
    <>
    {/* <Header title="back" showBack /> */}
    <ScrollView className="flex-1 bg-secondary">
      {approvals.map((approval) => (
        <View key={approval.id} className="bg-white m-4 rounded-xl shadow-sm">
          <View className="p-4">
            <View className="flex-row items-center mb-2">
              <Icon name={getIcon(approval.type)} size={24} color="#6366f1" />
              <Text className="text-lg font-semibold ml-2">{approval.title}</Text>
            </View>
            
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Amount: {approval.amount}</Text>
              <Text className="text-gray-600">{approval.date}</Text>
            </View>
            
            <Text className="text-gray-600 mb-3">Requested by: {approval.requestedBy}</Text>
            
            <View className="flex-row justify-end space-x-3">
              <TouchableOpacity 
                className="bg-danger px-4 py-2 rounded-lg"
                onPress={() => {/* Handle reject */}}
              >
                <Text className="text-white font-semibold">Reject</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                className="bg-primary px-4 py-2 rounded-lg"
                onPress={() => {/* Handle approve */}}
              >
                <Text className="text-white font-semibold">Approve</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      ))}
    </ScrollView>
    </>
  );
};

export default ApprovalsScreen;