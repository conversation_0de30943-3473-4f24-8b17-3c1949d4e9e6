import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Switch } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Header from '../../components/common/Header';

const SettingsScreen = () => {
  const [notifications, setNotifications] = React.useState(true);
  const [darkMode, setDarkMode] = React.useState(false);
  const [biometric, setBiometric] = React.useState(false);

  const settingSections = [
    {
      title: "Account",
      items: [
        {
          icon: "person",
          title: "Profile Information",
          type: "link"
        },
        {
          icon: "security",
          title: "Security",
          type: "link"
        },
        {
          icon: "fingerprint",
          title: "Biometric Login",
          type: "toggle",
          value: biometric,
          onChange: setBiometric
        }
      ]
    },
    {
      title: "Preferences",
      items: [
        {
          icon: "notifications",
          title: "Notifications",
          type: "toggle",
          value: notifications,
          onChange: setNotifications
        },
        {
          icon: "dark-mode",
          title: "Dark Mode",
          type: "toggle",
          value: darkMode,
          onChange: setDarkMode
        },
        {
          icon: "language",
          title: "Language",
          type: "link"
        }
      ]
    },
    {
      title: "Support",
      items: [
        {
          icon: "help",
          title: "Help Center",
          type: "link"
        },
        {
          icon: "policy",
          title: "Privacy Policy",
          type: "link"
        },
        {
          icon: "description",
          title: "Terms of Service",
          type: "link"
        }
      ]
    }
  ];

  return (
    <ScrollView className="flex-1 bg-secondary">
      {/* <Header title="back" showBack /> */}
      {settingSections.map((section, index) => (
        <View key={index} className="mb-6">
          <Text className="text-gray-500 text-sm font-medium mx-4 mb-2">
            {section.title}
          </Text>
          
          <View className="bg-white">
            {section.items.map((item, itemIndex) => (
              <TouchableOpacity 
                key={itemIndex}
                className={`flex-row items-center justify-between px-4 py-3 border-b border-gray-100
                  ${itemIndex === section.items.length - 1 ? 'border-b-0' : ''}`}
                onPress={() => {
                  if (item.type === 'link') {
                    /* Handle navigation */
                  }
                }}
              >
                <View className="flex-row items-center">
                  <Icon name={item.icon} size={24} color="#6366f1" />
                  <Text className="text-gray-800 ml-3">{item.title}</Text>
                </View>
                
                {item.type === 'toggle' ? (
                  <Switch
                    value={item.value}
                    onValueChange={item.onChange}
                    trackColor={{ false: "#D1D5DB", true: "#6366f1" }}
                    thumbColor={item.value ? "#ffffff" : "#ffffff"}
                  />
                ) : (
                  <Icon name="chevron-right" size={24} color="#6366f1" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ))}
    </ScrollView>
  );
};

export default SettingsScreen;