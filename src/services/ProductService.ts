import { UserRole } from '../data/mockData';
import DataService from './DataService';

/**
 * ProductService - A service for handling product-related operations
 * This service uses the DataService for data access and adds business logic
 */
class ProductService {
  /**
   * Get all products with pricing based on user role
   * @param userRole The role of the current user
   * @param categoryId Optional category filter
   */
  async getProducts(userRole: UserRole, categoryId?: number) {
    try {
      const products = await DataService.getProducts(categoryId);
      
      // Calculate prices based on user role
      return products.map(product => ({
        ...product,
        calculatedPrice: DataService.getProductPrice(product, userRole),
        // Hide price for public users
        showPrice: userRole !== UserRole.PUBLIC
      }));
    } catch (error) {
      console.error('Error fetching products:', error);
      return [];
    }
  }

  /**
   * Get a single product with pricing based on user role
   * @param productId The ID of the product to fetch
   * @param userRole The role of the current user
   */
  async getProductById(productId: string, userRole: UserRole) {
    try {
      const product = await DataService.getProductById(productId);
      
      if (!product) return null;
      
      return {
        ...product,
        calculatedPrice: DataService.getProductPrice(product, userRole),
        // Hide price for public users
        showPrice: userRole !== UserRole.PUBLIC
      };
    } catch (error) {
      console.error('Error fetching product:', error);
      return null;
    }
  }

  /**
   * Format price for display
   * @param price The price to format
   */
  formatPrice(price: number): string {
    return `₹${price.toLocaleString()}`;
  }

  /**
   * Get applicable discount schemes for a product
   * @param productId The ID of the product
   * @param userRole The role of the current user
   * @param userId The ID of the current user
   */
  async getProductDiscounts(productId: string, userRole: UserRole, userId: string) {
    try {
      const schemes = await DataService.getDiscountSchemes(userRole, userId);
      
      // Filter schemes applicable to this product
      return schemes.filter(scheme => 
        scheme.applicableProducts.includes(productId) && 
        scheme.applicableRoles.includes(userRole)
      );
    } catch (error) {
      console.error('Error fetching product discounts:', error);
      return [];
    }
  }
}

// Export as singleton
export default new ProductService();
