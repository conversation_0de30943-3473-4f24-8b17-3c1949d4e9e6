import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from './AuthApiService';

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: async ({ url, method, body, params }) => {
    try {
      let result;
      switch (method?.toUpperCase() || 'GET') {
        case 'GET':
          result = await AuthApiService.get(url, params);
          break;
        case 'POST':
          result = await AuthApiService.post(url, body);
          break;
        case 'PUT':
          result = await AuthApiService.put(url, body);
          break;
        case 'DELETE':
          result = await AuthApiService.delete(url);
          break;
        default:
          result = await AuthApiService.get(url, params);
      }
      return { data: result };
    } catch (error: any) {
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    }
  },
  tagTypes: ['Category', 'Product', 'Variant', 'Pricing'], // Add tag types for cache invalidation
  endpoints: (builder) => ({
    // Categories
    getCategories: builder.query({
      query: (params) => ({
        url: 'api/v1/catalog/categories',
        method: 'GET',
        params: { page: 0, count: 10, type: 1, ...params }
      }),
      transformResponse: (response) => response?.data || [],
      providesTags: (result) => 
        result 
          ? [...result.map(({ id }: { id: string }) => ({ type: 'Category', id })), 'Category']
          : ['Category']
    }),

    // Products
    getProducts: builder.query({
      query: (params) => ({
        url: 'api/v1/catalog/categories',
        method: 'GET',
        params: { page: 0, count: 100, type: 2, ...params }
      }),
      transformResponse: (response) => response?.data || [],
      providesTags: (result) => 
        result 
          ? [...result.map(({ id }: { id: string }) => ({ type: 'Product', id })), 'Product']
          : ['Product']
    }),

    getProductsByCategory: builder.query({
      query: (categoryId) => ({
        url: `api/v1/catalog/products/${categoryId}`,
        method: 'GET',
      }),
      transformResponse: (response) => response?.data || [],
      providesTags: (result, error, categoryId) => [
        { type: 'Product', id: `LIST-${categoryId}` }
      ]
    }),

    getFilteredProducts: builder.query({
      query: (params) => ({
        url: 'api/v1/catalog/filters',
        method: 'GET',
        params: {
          name: params.searchQuery || '',
          status: 1,
          type: 1,
          ...params
        }
      }),
      transformResponse: (response) => response.data?.data || [],
      providesTags: (result) => 
        result 
          ? [...result.map(({ id }: { id: string }) => ({ type: 'Product', id })), 'Product']
          : ['Product']
    }),

    getProductById: builder.query({
      query: (productId) => ({
        url: `api/v1/catalog/product/${productId}`,
        method: 'GET'
      }),
      transformResponse: (response) => response.data.productDTO || {},
      providesTags: (result, error, productId) => [
        { type: 'Product', id: productId }
      ]
    }),

    getVariantsbyProductId: builder.query({
      query: (productId) => ({
        url: `api/v1/variant/variants/${productId}`,
        method: 'GET'
      }),
      transformResponse: (response) => response.data || {},
      providesTags: (result, error, productId) => [
        { type: 'Variant', id: productId }
      ]
    }),

    getPricingbyProductAndVariantId: builder.query({
      query: ({productId, variantId}) => ({
        url: `api/v1/pricing/products-prices/${productId}?variantId=${variantId}`,
        method: 'GET'
      }),
      transformResponse: (response) => response.data || {},
      providesTags: (result, error, {productId, variantId}) => [
        { type: 'Pricing', id: `${productId}-${variantId}` }
      ]
    }),

    // Authentication
    login: builder.mutation({
      queryFn: async ({ username, password }) => {
        try {
          const user = await AuthApiService.authenticate(username, password);
          return { data: user };
        } catch (error: any) {
          return {
            error: {
              status: error.response?.status,
              data: error.response?.data || error.message
            }
          };
        }
      },
      invalidatesTags: ['Product', 'Category', 'Variant', 'Pricing'] // Invalidate all cache on login
    }),

    getAllProductPricing: builder.query({
      query: (params) => ({
        url: 'api/v1/pricing/products-prices',
        method: 'GET',
        params: { page: 0, count: 10, ...params }
      }),
      transformResponse: (response) => response?.data || [],
      providesTags: ['Pricing']
    }),

    // Add mutations for data updates that will invalidate cache
    updateProduct: builder.mutation({
      query: (product) => ({
        url: `api/v1/catalog/product/${product.id}`,
        method: 'PUT',
        body: product
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Product', id }]
    }),

    updatePricing: builder.mutation({
      query: ({productId, variantId, priceData}) => ({
        url: `api/v1/pricing/products-prices/${productId}?variantId=${variantId}`,
        method: 'PUT',
        body: priceData
      }),
      invalidatesTags: (result, error, {productId, variantId}) => [
        { type: 'Pricing', id: `${productId}-${variantId}` }
      ]
    }),
  }),
});

export const {
  useGetCategoriesQuery,
  useGetProductsQuery,
  useGetProductsByCategoryQuery,
  useGetFilteredProductsQuery,
  useGetProductByIdQuery,
  useGetVariantsbyProductIdQuery,
  useGetPricingbyProductAndVariantIdQuery,
  useLoginMutation,
  useUpdateProductMutation,
  useUpdatePricingMutation,
  useGetAllProductPricingQuery,
} = apiSlice;