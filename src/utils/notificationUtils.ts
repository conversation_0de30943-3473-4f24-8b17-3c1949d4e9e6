import { Notification, User, UserRole, Order } from '../data/mockData';
import { useNotifications } from '../context/NotificationContext';

// Helper function to get user role as string
export const getRoleString = (role: UserRole): string => {
  switch (role) {
    case UserRole.OOGE_TEAM:
      return 'Admin';
    case UserRole.SUPER_STOCKIST:
      return 'Super Stockist';
    case UserRole.DISTRIBUTOR:
      return 'Distributor';
    case UserRole.RETAILER:
      return 'Retailer';
    default:
      return 'User';
  }
};

// Generate notification for new order
export const createOrderNotification = (
  order: Order,
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'isRead'>) => void
): void => {
  const { placedBy, totalAmount } = order;

  addNotification({
    title: 'New Order Placed',
    message: `${placedBy.name} has placed a new order worth ₹${totalAmount.toLocaleString()}`,
    type: 'order',
    targetId: order.id,
    fromUser: {
      id: placedBy.id,
      name: placedBy.name,
      role: placedBy.role,
    }
  });
};

// Generate notification for new user registration
export const createUserRegistrationNotification = (
  user: User,
  registeredBy: User,
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'isRead'>) => void
): void => {
  const userRoleString = getRoleString(user.role);
  const registeredByRoleString = getRoleString(registeredBy.role);

  addNotification({
    title: `New ${userRoleString} Registration`,
    message: `${registeredBy.name} has registered a new ${userRoleString.toLowerCase()}: ${user.name}`,
    type: 'user',
    targetId: user.id,
    fromUser: {
      id: registeredBy.id,
      name: registeredBy.name,
      role: registeredBy.role,
      avatar: registeredBy.avatar
    }
  });
};



// Generate notification for order status update
export const createOrderStatusNotification = (
  order: Order,
  updatedBy: User,
  newStatus: string,
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'isRead'>) => void
): void => {
  let message = `Order #${order.id} has been ${newStatus}`;

  if (order.placedFor) {
    message += ` to ${order.placedFor.name}`;
  }

  addNotification({
    title: 'Order Status Update',
    message,
    type: 'order',
    targetId: order.id,
    fromUser: {
      id: updatedBy.id,
      name: updatedBy.name,
      role: updatedBy.role,
      avatar: updatedBy.avatar
    }
  });
};

// Hook to use notifications with utility functions
export const useNotificationUtils = <T>() => {
  const { addNotification } = useNotifications();

  return {
    createOrderNotification: (order: Order) => createOrderNotification(order, addNotification),
    createUserRegistrationNotification: (user: User, registeredBy: User) =>
      createUserRegistrationNotification(user, registeredBy, addNotification),

    createOrderStatusNotification: (order: Order, updatedBy: User, newStatus: string) =>
      createOrderStatusNotification(order, updatedBy, newStatus, addNotification),
  };
};
